package dao;

import pojo.Course;
import util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class CourseDao {
    Connection con = null;
    public List<Course> getCoursesByName(String name) {
        String sql = "SELECT * FROM s_course WHERE name LIKE ?";
        List<Course> courseList = new ArrayList<>();
        Connection con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setString(1, "%" + name + "%"); // 模糊查询
            ResultSet rs = prst.executeQuery();
            while (rs.next()) {
                Course course = new Course();
                course.setId(rs.getInt("id"));
                course.setName(rs.getString("name"));
                course.setTeacher_id(rs.getInt("teacher_id"));
                course.setMax_student_num(rs.getInt("max_student_num"));
                course.setInfo(rs.getString("info"));
                course.setSelected_num(rs.getInt("selected_num"));
                courseList.add(course);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return courseList;
    }
    /**
     * 添加课程
     */
    public int addCourse(Course course) {
        String sql = "INSERT INTO s_course (name, teacher_id, max_student_num, info, selected_num) VALUES (?, ?, ?, ?, ?)";
        int result = 0;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setString(1, course.getName());
            prst.setInt(2, course.getTeacher_id());
            prst.setInt(3, course.getMax_student_num());
            prst.setString(4, course.getInfo());
            prst.setInt(5, course.getSelected_num());
            result = prst.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return result;
    }

    /**
     * 通过ID删除课程
     */
    public int deleteCourse(int id) {
        String sql = "DELETE FROM s_course WHERE id = ?";
        int result = 0;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setInt(1, id);
            result = prst.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return result;
    }

    /**
     * 更新课程信息
     */
    public int updateCourse(Course course) {
        String sql = "UPDATE s_course SET name = ?, teacher_id = ?, max_student_num = ?, info = ?, selected_num = ? WHERE id = ?";
        int result = 0;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setString(1, course.getName());
            prst.setInt(2, course.getTeacher_id());
            prst.setInt(3, course.getMax_student_num());
            prst.setString(4, course.getInfo());
            prst.setInt(5, course.getSelected_num());
            prst.setInt(6, course.getId());
            System.out.println("执行更新SQL: " + sql);
            System.out.println("课程ID: " + course.getId() + ", 课程名称: " + course.getName() + ", 课程信息: " + course.getInfo());
            result = prst.executeUpdate();
            System.out.println("更新结果: " + result);
        } catch (SQLException e) {
            System.out.println("数据库更新异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return result;
    }

    /**
     * 通过ID查询课程
     */
    public Course getCourseById(int id) {
        String sql = "SELECT * FROM s_course WHERE id = ?";
        Course course = null;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setInt(1, id);
            ResultSet rs = prst.executeQuery();
            if (rs.next()) {
                course = new Course();
                course.setId(rs.getInt("id"));
                course.setName(rs.getString("name"));
                course.setTeacher_id(rs.getInt("teacher_id"));
                course.setMax_student_num(rs.getInt("max_student_num"));
                course.setInfo(rs.getString("info"));
                course.setSelected_num(rs.getInt("selected_num"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return course;
    }

    /**
     * 查询所有课程
     */
    public List<Course> getAllCourses() {
        String sql = "SELECT * FROM s_course";
        List<Course> courseList = new ArrayList<>();
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            ResultSet rs = prst.executeQuery();
            while (rs.next()) {
                Course course = new Course();
                course.setId(rs.getInt("id"));
                course.setName(rs.getString("name"));
                course.setTeacher_id(rs.getInt("teacher_id"));
                course.setMax_student_num(rs.getInt("max_student_num"));
                course.setInfo(rs.getString("info"));
                course.setSelected_num(rs.getInt("selected_num"));
                courseList.add(course);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return courseList;
    }

    /**
     * 学生选课
     */
    public int selectCourse(int courseId) {
        String sql = "UPDATE s_course SET selected_num = selected_num + 1 WHERE id = ? AND selected_num < max_student_num";
        int result = 0;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setInt(1, courseId);
            result = prst.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return result;
    }

    /**
     * 学生退课
     */
    public int dropCourse(int courseId) {
        String sql = "UPDATE s_course SET selected_num = selected_num - 1 WHERE id = ? AND selected_num > 0";
        int result = 0;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setInt(1, courseId);
            result = prst.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return result;
    }
}