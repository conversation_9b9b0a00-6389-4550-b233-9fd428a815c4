JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "2560"
			"$rowSpecs": "1600"
		} ) {
			name: "this"
			"title": "学生管理系统主界面"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
				"$columnSpecs": "default"
				"$rowSpecs": "default"
			} ) {
				name: "panel1"
				"background": new java.awt.Color( 0, 38, 114, 255 )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			menuBar: new FormContainer( "javax.swing.JMenuBar", new FormLayoutManager( class javax.swing.JMenuBar ) ) {
				name: "menuBar1"
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu1"
					"text": "系统设置"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/系统设置.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_psw"
						"text": "修改密码"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/修改密码.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_quit"
						"text": "退出系统"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/退出.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu2"
					"text": "学生管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_addStu"
						"text": "添加学生"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_stuList"
						"text": "学生列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/用户列表.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu4"
					"text": "班级管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级管理.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_addClass"
						"text": "添加班级"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_classList"
						"text": "班级列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级列表.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu5"
					"text": "教师管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_addTea"
						"text": "添加教师"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_teaList"
						"text": "教师列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu7"
					"text": "成绩管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级列表.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_grade"
						"text": "添加成绩"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_grademanage"
						"text": "成绩查询"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu8"
					"text": "课程信息查询"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/课程列表.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_course"
						"text": "课程查询"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu6"
					"text": "帮助"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/帮助.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_aboutUs"
						"text": "关于我们"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/关于我们.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu3"
				} )
			}
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 700, 540 )
		} )
	}
}
