/*
 * Created by JFormDesigner on Sat Jul 05 15:19:49 CST 2025
 */

package view;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.ClassDao;
import pojo.StudentClass;
import util.StringUtil;

/**
 * <AUTHOR>
 */
public class AddClassFrame extends JFrame {
    public AddClassFrame() {
        initComponents();
        btn_sure.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                // 从界面获取用户输入的数据
                String className = txt_name.getText().toString();
                String classInfo = txt_info.getText().toString();

                if(StringUtil.isEmpty(className)) {
                    JOptionPane.showMessageDialog(AddClassFrame.this, "班级名称不能为空，请输入班级名称!!!");
                    return;
                }

                if(StringUtil.isEmpty(classInfo)) {
                    JOptionPane.showMessageDialog(AddClassFrame.this, "班级信息不能为空，请输入班级信息!!！");
                    return;
                }

                StudentClass studentClass = new StudentClass();
                studentClass.setName(className);
                studentClass.setInfo(classInfo);

                //dao.addClass（studentClass）
                ClassDao dao = new ClassDao();
                boolean flag = dao.addClass(studentClass);

                if(flag) {
                    JOptionPane.showMessageDialog(AddClassFrame.this, "恭喜您，班级添加成功！");
                } else {
                    JOptionPane.showMessageDialog(AddClassFrame.this, "班级添加失败");
                }
                //（4）清空界面
                resetValue();
            }
        });

        //  btn_reset处理
        btn_reset.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                resetValue();
            }
        });
    }
    private void resetValue() {
        txt_name.setText("");
        txt_info.setText("");
    }




    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        label2 = new JLabel();
        txt_info = new JTextField();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u6dfb\u52a0\u73ed\u7ea7\u4fe1\u606f");
        setIconImage(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "93dlu, $lcgap, 104dlu, $lcgap, 81dlu",
            "2*(49dlu, $lgap), 58dlu"));

        //---- label1 ----
        label1.setText("\u73ed\u7ea7\u540d\u79f0\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label1, CC.xy(1, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(3, 1));

        //---- label2 ----
        label2.setText("\u73ed\u7ea7\u4fe1\u606f\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")));
        contentPane.add(label2, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_info, CC.xy(3, 3));

        //---- btn_sure ----
        btn_sure.setText("\u63d0\u4ea4");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_sure, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JLabel label2;
    private JTextField txt_info;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
