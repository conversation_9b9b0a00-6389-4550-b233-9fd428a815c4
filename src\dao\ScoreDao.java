package dao;

import pojo.Score;
import util.DBUtil;
import util.StringUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 成绩信息数据访问类
 * <AUTHOR>
 */
public class ScoreDao {
    Connection con = null;

    /**
     * 添加成绩
     */
    public boolean addScore(Score score) {
        String sql = "insert into s_score values(null,?,?,?)";
        con = DBUtil.getCon();
        try {
            PreparedStatement preparedStatement = con.prepareStatement(sql);
            preparedStatement.setInt(1, score.getStudentId());
            preparedStatement.setInt(2, score.getCourseId());
            preparedStatement.setInt(3, score.getScore());
            if (preparedStatement.executeUpdate() > 0)
                return true;
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return false;
    }

    /**
     * 查询成绩列表（支持按学生姓名查询）
     */
    public List<Score> getScoreList(Score score) {
        List<Score> retList = new ArrayList<Score>();
        StringBuffer sqlString = new StringBuffer(
            "select s.id, s.student_id, s.course_id, s.score, " +
            "st.name as student_name, c.name as course_name " +
            "from s_score s " +
            "left join s_student st on s.student_id = st.id " +
            "left join s_course c on s.course_id = c.id"
        );
        
        if (score.getStudentName() != null && !StringUtil.isEmpty(score.getStudentName())) {
            sqlString.append(" where st.name like '%" + score.getStudentName() + "%'");
        }
        
        con = DBUtil.getCon();
        try {
            PreparedStatement preparedStatement = con.prepareStatement(sqlString.toString());
            ResultSet executeQuery = preparedStatement.executeQuery();
            while (executeQuery.next()) {
                Score s = new Score();
                s.setId(executeQuery.getInt("id"));
                s.setStudentId(executeQuery.getInt("student_id"));
                s.setCourseId(executeQuery.getInt("course_id"));
                s.setScore(executeQuery.getInt("score"));
                s.setStudentName(executeQuery.getString("student_name"));
                s.setCourseName(executeQuery.getString("course_name"));
                retList.add(s);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return retList;
    }

    /**
     * 删除成绩
     */
    public boolean delete(int id) {
        String sql = "delete from s_score where id=?";
        con = DBUtil.getCon();
        try {
            PreparedStatement preparedStatement = con.prepareStatement(sql);
            preparedStatement.setInt(1, id);
            if (preparedStatement.executeUpdate() > 0) {
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return false;
    }

    /**
     * 更新成绩
     */
    public boolean update(Score score) {
        String sql = "update s_score set student_id=?, course_id=?, score=? where id=?";
        con = DBUtil.getCon();
        try {
            PreparedStatement preparedStatement = con.prepareStatement(sql);
            preparedStatement.setInt(1, score.getStudentId());
            preparedStatement.setInt(2, score.getCourseId());
            preparedStatement.setInt(3, score.getScore());
            preparedStatement.setInt(4, score.getId());
            if (preparedStatement.executeUpdate() > 0) {
                return true;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return false;
    }

    /**
     * 根据ID查询成绩
     */
    public Score getScoreById(int id) {
        String sql = "select s.id, s.student_id, s.course_id, s.score, " +
                    "st.name as student_name, c.name as course_name " +
                    "from s_score s " +
                    "left join s_student st on s.student_id = st.id " +
                    "left join s_course c on s.course_id = c.id " +
                    "where s.id = ?";
        Score score = null;
        con = DBUtil.getCon();
        try {
            PreparedStatement prst = con.prepareStatement(sql);
            prst.setInt(1, id);
            ResultSet executeQuery = prst.executeQuery();
            if (executeQuery.next()) {
                score = new Score();
                score.setId(executeQuery.getInt("id"));
                score.setStudentId(executeQuery.getInt("student_id"));
                score.setCourseId(executeQuery.getInt("course_id"));
                score.setScore(executeQuery.getInt("score"));
                score.setStudentName(executeQuery.getString("student_name"));
                score.setCourseName(executeQuery.getString("course_name"));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeCon(con);
        }
        return score;
    }
}
