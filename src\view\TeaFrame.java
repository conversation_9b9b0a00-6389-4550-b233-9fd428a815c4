/*
 * Created by <PERSON><PERSON>orm<PERSON>esigner on Fri Jul 11 18:16:40 CST 2025
 */

package view;

import java.awt.*;
import javax.swing.*;
import com.jgoodies.forms.factories.*;

import com.jgoodies.forms.layout.*;
import pojo.Admin;
import pojo.Teacher;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * <AUTHOR>
 */
public class TeaFrame extends JFrame {
    public static String userType;
    public static Teacher teacher;
    public TeaFrame(String mUserType, Teacher mTeacher) {
        userType = mUserType;
        teacher = mTeacher; // 保存教师对象
        initComponents();
        item_psw1.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //打开修改密码窗口，并设置为可见
                new EditPswFrame().setVisible(true);
            }
        });
        //item_quit:事件处理
        item_quit1.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //弹出一个确认对话框
                if(JOptionPane.showConfirmDialog(TeaFrame.this,"确认退出吗？")==JOptionPane.OK_OPTION)
                {
                    System.exit(0);
                }
            }
        });
        item_addStu.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开添加学生窗口并可见
                new AddStuFrame().setVisible(true);
            }
        });
        //item_stuList：事件处理    事件源：item_stuList    监听器：匿名类对象
        item_stuList.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开学生列表窗口，并设置为可见
                new StuManageFrame().setVisible(true);
            }
        });
        //item_addClass事件处理
        item_addClass.addActionListener(new ActionListener() {


            @Override
            public void actionPerformed(ActionEvent e) {
                //打开添加班级窗口，并设置为可见
                new AddClassFrame().setVisible(true);
            }
        });
        item_classList.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new ClassManageFrame().setVisible(true);
            }
        });
        item_teaList.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new TeaManageFrame().setVisible(true);
            }
        });
        item_class.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开课程查询以及修改窗口并可见
                new CourseMangerFrame().setVisible(true);
            }
        });
        item_addCourse.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开添加课程窗口并可见
                new AddCourse().setVisible(true);
            }
        });
        item_stugrade.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开成绩输入窗口可见
                new AddScoreFrame().setVisible(true);
            }
        });
        item_stugrademanage.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开成绩管理窗口可见
                new GradeManageFrame().setVisible(true);
            }
        });
        item_help.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new AboutUsFrame().setVisible(true);
            }
        });

    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        menuBar1 = new JMenuBar();
        menu1 = new JMenu();
        item_psw1 = new JMenuItem();
        item_quit1 = new JMenuItem();
        menu7 = new JMenu();
        item_addStu = new JMenuItem();
        item_stuList = new JMenuItem();
        menu6 = new JMenu();
        item_addClass = new JMenuItem();
        item_classList = new JMenuItem();
        menu2 = new JMenu();
        item_teaList = new JMenuItem();
        menu3 = new JMenu();
        item_class = new JMenuItem();
        item_addCourse = new JMenuItem();
        menu4 = new JMenu();
        item_stugrade = new JMenuItem();
        item_stugrademanage = new JMenuItem();
        menu5 = new JMenu();
        item_help = new JMenuItem();
        panel1 = new JPanel();

        //======== this ========
        setTitle("\u6559\u5e08\u4fe1\u606f\u7cfb\u7edf");
        setIconImage(new ImageIcon(getClass().getResource("/\u5b66\u751f.png")).getImage());
        setResizable(false);
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "1920px",
            "1020px"));

        //======== menuBar1 ========
        {

            //======== menu1 ========
            {
                menu1.setText("\u7cfb\u7edf\u8bbe\u7f6e");
                menu1.setIcon(new ImageIcon(getClass().getResource("/\u7cfb\u7edf\u8bbe\u7f6e.png")));

                //---- item_psw1 ----
                item_psw1.setText("\u4fee\u6539\u5bc6\u7801");
                item_psw1.setIcon(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")));
                menu1.add(item_psw1);

                //---- item_quit1 ----
                item_quit1.setText("\u9000\u51fa\u7cfb\u7edf");
                item_quit1.setIcon(new ImageIcon(getClass().getResource("/\u9000\u51fa.png")));
                menu1.add(item_quit1);
            }
            menuBar1.add(menu1);

            //======== menu7 ========
            {
                menu7.setText("\u5b66\u751f\u7ba1\u7406");
                menu7.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));

                //---- item_addStu ----
                item_addStu.setText("\u6dfb\u52a0\u5b66\u751f");
                item_addStu.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu7.add(item_addStu);

                //---- item_stuList ----
                item_stuList.setText("\u5b66\u751f\u5217\u8868");
                item_stuList.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u5217\u8868.png")));
                menu7.add(item_stuList);
            }
            menuBar1.add(menu7);

            //======== menu6 ========
            {
                menu6.setText("\u73ed\u7ea7\u7ba1\u7406");
                menu6.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u7ba1\u7406.png")));

                //---- item_addClass ----
                item_addClass.setText("\u6dfb\u52a0\u73ed\u7ea7");
                item_addClass.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu6.add(item_addClass);

                //---- item_classList ----
                item_classList.setText("\u73ed\u7ea7\u5217\u8868");
                item_classList.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u5217\u8868.png")));
                menu6.add(item_classList);
            }
            menuBar1.add(menu6);

            //======== menu2 ========
            {
                menu2.setText("\u6559\u5e08\u4fe1\u606f\u67e5\u8be2");
                menu2.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));

                //---- item_teaList ----
                item_teaList.setText("\u6559\u5e08\u5217\u8868");
                item_teaList.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
                menu2.add(item_teaList);
            }
            menuBar1.add(menu2);

            //======== menu3 ========
            {
                menu3.setText("\u8bfe\u7a0b\u4fe1\u606f\u67e5\u8be2");
                menu3.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));

                //---- item_class ----
                item_class.setText("\u8bfe\u7a0b\u67e5\u8be2");
                item_class.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
                menu3.add(item_class);

                //---- item_addCourse ----
                item_addCourse.setText("\u6dfb\u52a0\u8bfe\u7a0b");
                item_addCourse.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu3.add(item_addCourse);
            }
            menuBar1.add(menu3);

            //======== menu4 ========
            {
                menu4.setText("\u6210\u7ee9\u7ba1\u7406");
                menu4.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u5217\u8868.png")));

                //---- item_stugrade ----
                item_stugrade.setText("\u6210\u7ee9\u8f93\u5165");
                item_stugrade.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));
                menu4.add(item_stugrade);

                //---- item_stugrademanage ----
                item_stugrademanage.setText("\u6210\u7ee9\u7ba1\u7406");
                item_stugrademanage.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u5217\u8868.png")));
                menu4.add(item_stugrademanage);
            }
            menuBar1.add(menu4);

            //======== menu5 ========
            {
                menu5.setText("\u5e2e\u52a9");
                menu5.setIcon(new ImageIcon(getClass().getResource("/\u5e2e\u52a9.png")));

                //---- item_help ----
                item_help.setText("\u5173\u4e8e\u6211\u4eec");
                item_help.setIcon(new ImageIcon(getClass().getResource("/\u5173\u4e8e\u6211\u4eec.png")));
                menu5.add(item_help);
            }
            menuBar1.add(menu5);
        }
        setJMenuBar(menuBar1);

        //======== panel1 ========
        {
            panel1.setBackground(new Color(0x33ffcc));
            panel1.setLayout(new FormLayout(
                "default",
                "default"));
        }
        contentPane.add(panel1, CC.xy(1, 1, CC.FILL, CC.FILL));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JMenuBar menuBar1;
    private JMenu menu1;
    private JMenuItem item_psw1;
    private JMenuItem item_quit1;
    private JMenu menu7;
    private JMenuItem item_addStu;
    private JMenuItem item_stuList;
    private JMenu menu6;
    private JMenuItem item_addClass;
    private JMenuItem item_classList;
    private JMenu menu2;
    private JMenuItem item_teaList;
    private JMenu menu3;
    private JMenuItem item_class;
    private JMenuItem item_addCourse;
    private JMenu menu4;
    private JMenuItem item_stugrade;
    private JMenuItem item_stugrademanage;
    private JMenu menu5;
    private JMenuItem item_help;
    private JPanel panel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
