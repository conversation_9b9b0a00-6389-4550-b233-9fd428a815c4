package view;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.ClassDao;
import pojo.StudentClass;
import util.StringUtil;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Vector;

public class ClassManageFrame extends JFrame {
    private List<StudentClass> classList; // 添加这行声明

    public ClassManageFrame() {
        initComponents();

        // 设置表组件的显示模式
        DefaultTableModel dtm = new DefaultTableModel();
        dtm.setDataVector(new Object[][]{}, new String[]{"班级编号", "班级名称", "班级信息介绍"});
        table1 .setModel(dtm);
        // 初始化事件处理器
        initEventHandlers();
        //初始数据
        setTable(new StudentClass());
        
    }

    private void initEventHandlers() {
        // 查询按钮的ACtionEvent事件处理
        btn_query.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String className = txt_name.getText().toString();
                StudentClass sc = new StudentClass();
                sc.setName(className);
                //dao查询，把查询到的结果设置到table中
                setTable(sc);
            }
        });

        // 选中表中某一项时，将该班级数据设置到页面下方的控件上
        table1.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                selectedTableRow();
            }
        });

        // btn_update事件处理
        btn_update.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "请先选中一条数据再进行修改！");
                    return;
                }
                //从界面中获取用户修改过后的数据
                String className = txt_name2.getText().toString();
                String classInfo = txt_info.getText().toString();

                if(StringUtil.isEmpty(className)) {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "班级名称不能为空！");
                    return;
                }
                StudentClass sc = new StudentClass();
                sc.setId(Integer.parseInt(table1.getValueAt(srow, 0).toString()));
                sc.setName(className);
                sc.setInfo(classInfo);
                //dao.update
                ClassDao dao = new ClassDao();
                boolean flag = dao.update(sc);
                //根据返回值提示用户不同的信息
                if(flag) {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "恭喜您，修改成功！");
                } else {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "修改失败！！！");
                }
                //及时更新表格组件中的数据
                setTable(new StudentClass());
            }
        });

        btn_delete.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //1、判断用户有没有选中表中的数据，没有则提示
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "请先选中要删除的数据");
                    return;
                }
                //2、从表中获取选中的那条数据的班级的编号，dao.delete()
                int classId = Integer.parseInt(table1.getValueAt(srow, 0).toString());

                ClassDao dao = new ClassDao();
                boolean flag = dao.delete(classId);
                //3、根据返回值不同执行不同的提示
                if(flag) {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "删除成功！");
                } else {
                    JOptionPane.showMessageDialog(ClassManageFrame.this, "删除失败，请重试！");
                }
                //4、及时更新表中的数据
                setTable(new StudentClass());
            }
        });
    }

    // 选中行显示详情
    private void selectedTableRow() {
        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        int srow = table1.getSelectedRow();
        String className = dtm.getValueAt(srow, 1).toString();
        String classInfo = dtm.getValueAt(srow, 2).toString();

        txt_name2.setText(className);
        txt_info.setText(classInfo); // 修正为setText()
    }

    //选中表中某一项时，将该班级数据设置到页面下方的控件上
    private void setTable(StudentClass sc) {
        ClassDao dao = new ClassDao();
        classList = dao.getClassList(sc);
        //把选中的这一行数据的每个字段的字段值提取出来
        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        dtm.setRowCount(0);

        for(StudentClass studentClass : classList) {
            Vector v = new Vector();
            v.add(studentClass.getId());
            v.add(studentClass.getName());
            v.add(studentClass.getInfo());
            dtm.addRow(v);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label2 = new JLabel();
        txt_name2 = new JTextField();
        btn_update = new JButton();
        label3 = new JLabel();
        txt_info = new JTextField();
        btn_delete = new JButton();

        //======== this ========
        setTitle("\u73ed\u7ea7\u4fe1\u606f\u7ba1\u7406");
        setIconImage(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "56dlu, $lcgap, 62dlu, $lcgap, 191dlu, $lcgap, 47dlu, $lcgap, 57dlu",
            "62dlu, $lgap, 138dlu, $lgap, 48dlu, $lgap, 56dlu"));

        //---- label1 ----
        label1.setText("\u73ed\u7ea7\u540d\u79f0\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- btn_query ----
        btn_query.setText("\u67e5\u8be2");
        btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
        contentPane.add(btn_query, CC.xy(7, 1));

        //======== scrollPane1 ========
        {
            scrollPane1.setViewportView(table1);
        }
        contentPane.add(scrollPane1, CC.xywh(3, 3, 5, 1, CC.FILL, CC.FILL));

        //---- label2 ----
        label2.setText("\u73ed\u7ea7\u540d\u79f0\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label2, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name2, CC.xy(5, 5));

        //---- btn_update ----
        btn_update.setText("\u786e\u8ba4\u4fee\u6539");
        btn_update.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_update, CC.xy(7, 5));

        //---- label3 ----
        label3.setText("\u73ed\u7ea7\u4fe1\u606f\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")));
        contentPane.add(label3, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_info, CC.xy(5, 7));

        //---- btn_delete ----
        btn_delete.setText("\u5220\u9664");
        btn_delete.setIcon(new ImageIcon(getClass().getResource("/\u5220\u9664.png")));
        contentPane.add(btn_delete, CC.xy(7, 7));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label2;
    private JTextField txt_name2;
    private JButton btn_update;
    private JLabel label3;
    private JTextField txt_info;
    private JButton btn_delete;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
