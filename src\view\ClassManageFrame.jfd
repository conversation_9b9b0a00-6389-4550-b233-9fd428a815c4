JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "56dlu, labelcompgap, 62dlu, labelcompgap, 191dlu, labelcompgap, 47dlu, labelcompgap, 57dlu"
			"$rowSpecs": "62dlu, linegap, 138dlu, linegap, 48dlu, linegap, 56dlu"
		} ) {
			name: "this"
			"title": "班级信息管理"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/班级介绍.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "班级名称："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
				"gridX": 3
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_query"
				"text": "查询"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "scrollPane1"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "table1"
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"gridWidth": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "班级名称："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name2"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_update"
				"text": "确认修改"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "班级信息："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级介绍.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_info"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_delete"
				"text": "删除"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/删除.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 7
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 785, 540 )
		} )
	}
}
