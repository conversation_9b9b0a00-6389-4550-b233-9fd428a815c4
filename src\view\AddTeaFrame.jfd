JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "45dlu, labelcompgap, 83dlu, labelcompgap, 92dlu, labelcompgap, 52dlu"
			"$rowSpecs": "42dlu, linegap, 48dlu, linegap, 49dlu, linegap, 45dlu, linegap, 43dlu, linegap, 44dlu"
		} ) {
			name: "this"
			"title": "教师添加窗口"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "教师姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "教师性别："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/性别.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
				"$columnSpecs": "default, labelcompgap, default"
				"$rowSpecs": "default"
			} ) {
				name: "panel1"
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_male"
					"text": "男"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 1
					"gridY": 1
				} )
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_female"
					"text": "女"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 3
					"gridY": 1
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "教师职称："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/职称评定.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_title"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "教师年龄："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/年龄.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_age"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label5"
				"text": "登陆密码："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/密码.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_psw"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 9
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_sure"
				"text": "确认添加"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 11
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "重置表单"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 11
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 515, 505 )
		} )
	}
}
