/*
 * Created by JFormDesigner on Sun Jun 29 19:05:54 CST 2025
 */

package view;

import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.ClassDao;
import dao.StudentDao;
import pojo.Student;
import pojo.StudentClass;
import util.StringUtil;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AddStuFrame extends JFrame {
    private ButtonGroup group;
    public AddStuFrame() {
        //1、页面初始化的代码，放到构造方法的第一行
        initComponents();
        //2、下拉列表的设置，从数据库表中把班级信息读取过来，设置到下拉别表
        setStuClassInfo();
        //3、单选按钮三选一的效果
        group=new ButtonGroup();
        group.add(rb_male);
        group.add(rb_female);
        group.add(rb_secret);
        //4、btn_sure进行ActionEvent事件处理
        btn_sure.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //(1)从界面获取用户输入的数据，并封装到一个student对象中
                //（1）打断点后，以debug模式启动程序时，程序执行到断点处会中断，手动单步调试代码，观察变量的值的变化
                //（2）以debug模式启动程序
                //（3）单步调试：步过、步入、步出
                String stuName= txt_name.getText().toString();
                String stuPsw= txt_psw.getText().toString();
                StudentClass stuClass= (StudentClass) cb_class.getSelectedItem();
                String sex=rb_male.isSelected()?rb_male.getText():rb_female.isSelected()? rb_female.getText() : rb_secret.getText();
                if(StringUtil.isEmpty(stuName)){
                    JOptionPane.showMessageDialog(AddStuFrame.this,"学生姓名不能为空，请输入学生姓名！！！");
                    return;
                }
                if(StringUtil.isEmpty(stuPsw)){
                    JOptionPane.showMessageDialog(AddStuFrame.this,"学生密码不能为空，请输入学生密码！！！");
                    return;
                }
                Student stu=new Student();
                stu.setName(stuName);
                stu.setPassword(stuPsw);
                stu.setClassId(stuClass.getId());
                stu.setSex(sex);
                //(2)dao.addStudent（stu）
                StudentDao dao=new StudentDao();
                boolean flag=dao.addStudent(stu);
                //（3）根据返回值完成对用户的提示
                if(flag){
                    JOptionPane.showMessageDialog(AddStuFrame.this,"恭喜您添加成功！！！");
                }else{
                    JOptionPane.showMessageDialog(AddStuFrame.this,"添加失败");
                }
                //（4）清空界面
                resetValue();
            }
        });
        //5、btn_reset进行ActionEvent事件处理
        btn_reset.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                resetValue();
            }
        });
    }
    //清空界面
    private void resetValue() {
       txt_name.setText("");
       txt_psw.setText("");
       cb_class.setSelectedIndex(0);
       group.clearSelection();
       rb_male.setSelected(true);
    }

    //从数据库表中把班级信息读取过来，设置到下拉别表
    private void setStuClassInfo() {
        //(1)创建dao，dao.getClassList（）
        ClassDao dao=new ClassDao();
        List<StudentClass>classList=dao.getClassList(new StudentClass());
        //(2)把班级列表中的每个对象设置到下来列表cb_class的每一项上
        for(StudentClass sc:classList){
            cb_class.addItem(sc);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        label2 = new JLabel();
        cb_class = new JComboBox();
        label3 = new JLabel();
        txt_psw = new JTextField();
        label4 = new JLabel();
        rb_male = new JCheckBox();
        rb_female = new JCheckBox();
        rb_secret = new JCheckBox();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u6dfb\u52a0\u5b66\u751f\u7a97\u53e3");
        setIconImage(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "82dlu, 2*($lcgap, 33dlu), $lcgap, 50dlu, $lcgap, 37dlu",
            "53dlu, 4*($lgap, 35dlu)"));

        //---- label1 ----
        label1.setText("\u5b66\u751f\u59d3\u540d\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));
        contentPane.add(label1, CC.xy(1, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xywh(3, 1, 6, 1));

        //---- label2 ----
        label2.setText("\u6240\u5c5e\u73ed\u7ea7\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label2, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_class, CC.xywh(3, 3, 5, 1));

        //---- label3 ----
        label3.setText("\u767b\u5f55\u5bc6\u7801\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label3, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_psw, CC.xywh(3, 5, 5, 1));

        //---- label4 ----
        label4.setText("\u5b66\u751f\u6027\u522b\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/\u6027\u522b.png")));
        contentPane.add(label4, CC.xy(1, 7, CC.RIGHT, CC.DEFAULT));

        //---- rb_male ----
        rb_male.setText("\u7537");
        contentPane.add(rb_male, CC.xy(3, 7));

        //---- rb_female ----
        rb_female.setText("\u5973");
        contentPane.add(rb_female, CC.xy(5, 7));

        //---- rb_secret ----
        rb_secret.setText("\u4fdd\u5bc6");
        contentPane.add(rb_secret, CC.xy(7, 7));

        //---- btn_sure ----
        btn_sure.setText("\u786e\u8ba4");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_sure, CC.xy(1, 9, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(7, 9));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JLabel label2;
    private JComboBox cb_class;
    private JLabel label3;
    private JTextField txt_psw;
    private JLabel label4;
    private JCheckBox rb_male;
    private JCheckBox rb_female;
    private JCheckBox rb_secret;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
