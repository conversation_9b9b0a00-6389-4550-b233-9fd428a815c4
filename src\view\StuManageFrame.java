/*
 * Created by JFormDesigner on Mon Jun 30 17:50:13 CST 2025
 */

package view;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;

import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.ClassDao;
import dao.StudentDao;
import pojo.Student;
import pojo.StudentClass;
import util.StringUtil;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Vector;

/**
 * <AUTHOR>
 */
public class StuManageFrame extends JFrame {
    private ButtonGroup group;
    private List<StudentClass> classList;
    //1、自动生成的初始化界面的代码，放到构造方法的第一行
    public StuManageFrame() {
        initComponents();
        //2、三个单选按钮实现单选效果
        group = new ButtonGroup();
        group.add(rb_male);
        group.add(rb_female);
        group.add(rb_secret);
        //3、cb_class下拉列表显示从数据库表中查询过来的班级
        setStuClassInfo();
        //4、设置表组件的显示模式
        DefaultTableModel dtm=new DefaultTableModel();
        dtm.setDataVector(new Object[][]{},new String[]{"学生编号","学生姓名","学生班级","学生性别","登录密码"});
        table1.setModel(dtm);

        //5、初始加载所有学生数据
        setTable(new Student());
        //5、查询按钮的ACtionEvent事件处理
        btn_query.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //1、获取用户从界面输入的姓名和班级信息
                String stuName=txt_name.getText().toString();
                StudentClass stuClass=(StudentClass)cb_class.getSelectedItem();
                Student stu=new Student();
                stu.setName(stuName);
                stu.setClassId(stuClass.getId());

                //2、dao查询，把查询到的结果设置到table中
                setTable(stu);
            }
        });
        //6、选中表中某一项时，将该学生数据设置到页面下方的控件上
        table1.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                selectedTableRow();
            }
        });
        //7、btn_update事件处理，实现修改数据的功能
        btn_update.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //1、判断一下表中数据有没有被选中，如果没有选中数据，需提示用户先选中才能修改
                int srow=table1.getSelectedRow();
                if(srow==-1)
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"请选中需要修改的数据");
                    return;
                }
                //2、从界面中获取用户修改过后的数据
                String stuName=txt_name2.getText().toString();
                String stuPsw=pf_psw.getText().toString();
                if(StringUtil.isEmpty(stuName))
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"请填写学生姓名");
                    return;
                }
                if(StringUtil.isEmpty(stuPsw))
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"请填写学生密码");
                    return;
                }
                StudentClass sc= (StudentClass) cb_class2.getSelectedItem();
                String stuSex=rb_male.isSelected()?rb_male.getText():rb_female.isSelected()?rb_female.getText():rb_secret.getText();
                //把数据封装到一个student对象中
                Student stu=new Student();
                //从选中的行中获取学生编号
                int stuId=Integer.parseInt(table1.getValueAt(srow,0).toString());
                //封装到stu中
                stu.setId(stuId);
                stu.setName(stuName);
                stu.setClassId(sc.getId());
                stu.setSex(stuSex);
                stu.setPassword(stuPsw);
                //3、dao.update
                StudentDao dao=new StudentDao();
                boolean flag=dao.update(stu);
                //4、根据返回值提示用户不同的信息
                if(flag)
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"更新成功");
                }else{
                    JOptionPane.showMessageDialog(StuManageFrame.this,"更新失败");
                }
                //及时更新表格组件中的数据
                setTable(new Student());
            }
        });
        //8、删除按钮的事件处理过程
        btn_delete.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //1、判断用户有没有选中表中的数据，没有则提示
                int srow=table1.getSelectedRow();
                if(srow==-1)
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"请在表中选中待删除的数据");
                    return;
                }
                //2、从表中获取选中的那条数据的学生的编号，dao.delete()
                int stuId=Integer.parseInt(table1.getValueAt(srow,0).toString());
                StudentDao dao=new StudentDao();
                boolean flag=dao.delete(stuId);
                //3、根据返回值不同执行不同的提示
                if(flag)
                {
                    JOptionPane.showMessageDialog(StuManageFrame.this,"删除成功");
                }else{
                    JOptionPane.showMessageDialog(StuManageFrame.this,"删除失败");
                }
                //4、及时更新表中的数据
                setTable(new Student());
            }
        });
    }
    //6、选中表中某一项时，将该学生数据设置到页面下方的控件上
    private void selectedTableRow() {
        //1、把选中的这一行数据的每个字段的字段值提取出来
        DefaultTableModel dtm=(DefaultTableModel)table1.getModel();
        int srow=table1.getSelectedRow();
        String stuName=dtm.getValueAt(srow,1).toString();
        String stuClass=dtm.getValueAt(srow,2).toString();
        String stuSex=dtm.getValueAt(srow,3).toString();
        String stuPsw=dtm.getValueAt(srow,4).toString();
        //2、设置到界面
        txt_name2.setText(stuName);
        pf_psw.setText(stuPsw);
        //3、把stuClass设置到下拉列表上
        for(int i=0;i<cb_class2.getItemCount();i++)
        {
            StudentClass sc= (StudentClass) cb_class2.getItemAt(i);
            if(sc.getName().equals(stuClass))
            {
                cb_class2.setSelectedIndex(i);
            }
        }
        //把性别设置到单选按钮上
        group.clearSelection();
        if(stuSex.equals(rb_male.getText()))
        {
            rb_male.setSelected(true);
        }
        if(stuSex.equals(rb_female.getText()))
        {
            rb_female.setSelected(true);
        }
        if(stuSex.equals(rb_secret.getText()))
        {
            rb_secret.setSelected(true);
        }
    }

    //dao查询，把查询到的结果设置到table中
    private void setTable(Student stu) {
        //1、dao.getStudentList（）
        StudentDao dao=new StudentDao();
        List<Student>stuList=dao.getStudentList(stu);
        //2、查询结果进行渲染
        DefaultTableModel dtm=(DefaultTableModel) table1.getModel();
        dtm.setRowCount(0);
        for(Student student:stuList){
            Vector v=new Vector();
            v.add(student.getId());
            v.add(student.getName());
            v.add(getClassNameById(student.getClassId()));
            v.add(student.getSex());
            v.add(student.getPassword());
            dtm.addRow(v);

        }
    }

    private String getClassNameById(int classId) {
        for(StudentClass sc:classList){
            if(classId==sc.getId()){
                return sc.getName();
            }
        }
        return "";
    }

    //下拉列表显示从数据库表中查询过来的班级
    private void setStuClassInfo() {
        //（1）dao.getClassList()
        ClassDao dao=new ClassDao();
        classList=dao.getClassList(new StudentClass());
        //（2）遍历班级列表，渲染到下拉列表上
        for(StudentClass sc:classList){
            cb_class.addItem(sc);
            cb_class2.addItem(sc);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        label2 = new JLabel();
        cb_class = new JComboBox();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label3 = new JLabel();
        txt_name2 = new JTextField();
        label4 = new JLabel();
        panel1 = new JPanel();
        rb_male = new JRadioButton();
        rb_female = new JRadioButton();
        rb_secret = new JRadioButton();
        btn_update = new JButton();
        label5 = new JLabel();
        cb_class2 = new JComboBox();
        label6 = new JLabel();
        pf_psw = new JTextField();
        btn_delete = new JButton();

        //======== this ========
        setTitle("\u5b66\u751f\u5217\u8868\u7a97\u53e3");
        setIconImage(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "25dlu, 3*($lcgap, 75dlu), $lcgap, 82dlu, $lcgap, 75dlu, $lcgap, 25dlu",
            "41dlu, $lgap, 120dlu, 2*($lgap, 41dlu)"));

        //---- label1 ----
        label1.setText("\u5b66\u751f\u59d3\u540d\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- label2 ----
        label2.setText("\u6240\u5c5e\u73ed\u7ea7\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label2, CC.xy(7, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_class, CC.xy(9, 1));

        //---- btn_query ----
        btn_query.setText("\u67e5\u8be2");
        btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
        contentPane.add(btn_query, CC.xy(11, 1, CC.FILL, CC.DEFAULT));

        //======== scrollPane1 ========
        {
            scrollPane1.setViewportView(table1);
        }
        contentPane.add(scrollPane1, CC.xywh(3, 3, 9, 1, CC.FILL, CC.FILL));

        //---- label3 ----
        label3.setText("\u5b66\u751f\u59d3\u540d\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));
        contentPane.add(label3, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name2, CC.xy(5, 5));

        //---- label4 ----
        label4.setText("\u5b66\u751f\u6027\u522b\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/\u6027\u522b.png")));
        contentPane.add(label4, CC.xy(7, 5, CC.RIGHT, CC.DEFAULT));

        //======== panel1 ========
        {
            panel1.setLayout(new FormLayout(
                "2*(default, $lcgap), default",
                "default"));

            //---- rb_male ----
            rb_male.setText("\u7537");
            panel1.add(rb_male, CC.xy(1, 1));

            //---- rb_female ----
            rb_female.setText("\u5973");
            panel1.add(rb_female, CC.xy(3, 1));

            //---- rb_secret ----
            rb_secret.setText("\u4fdd\u5bc6");
            panel1.add(rb_secret, CC.xy(5, 1));
        }
        contentPane.add(panel1, CC.xy(9, 5));

        //---- btn_update ----
        btn_update.setText("\u786e\u8ba4\u4fee\u6539");
        btn_update.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_update, CC.xy(11, 5, CC.FILL, CC.DEFAULT));

        //---- label5 ----
        label5.setText("\u6240\u5c5e\u73ed\u7ea7\uff1a");
        label5.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label5, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_class2, CC.xy(5, 7));

        //---- label6 ----
        label6.setText("\u767b\u9646\u5bc6\u7801\uff1a");
        label6.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label6, CC.xy(7, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_psw, CC.xy(9, 7));

        //---- btn_delete ----
        btn_delete.setText("\u5220\u9664\u5b66\u751f");
        btn_delete.setIcon(new ImageIcon(getClass().getResource("/\u5220\u9664.png")));
        contentPane.add(btn_delete, CC.xy(11, 7));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JLabel label2;
    private JComboBox cb_class;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label3;
    private JTextField txt_name2;
    private JLabel label4;
    private JPanel panel1;
    private JRadioButton rb_male;
    private JRadioButton rb_female;
    private JRadioButton rb_secret;
    private JButton btn_update;
    private JLabel label5;
    private JComboBox cb_class2;
    private JLabel label6;
    private JTextField pf_psw;
    private JButton btn_delete;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
