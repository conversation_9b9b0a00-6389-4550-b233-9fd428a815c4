JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "55dlu, labelcompgap, 91dlu, labelcompgap, 88dlu, labelcompgap, 71dlu, labelcompgap, 68dlu, labelcompgap, 55dlu"
			"$rowSpecs": "48dlu, linegap, 154dlu, linegap, 42dlu, linegap, 39dlu, linegap, 41dlu"
		} ) {
			name: "this2"
			"title": "课程信息管理"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/课程列表.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "课程名称："
				"icon": &SwingIcon0 new com.jformdesigner.model.SwingIcon( 0, "/课程列表.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridWidth": 3
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_query"
				"text": "查询"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "scrollPane1"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "table1"
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"gridWidth": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"gridHeight": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "课程名称："
				"icon": #SwingIcon0
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
				"gridX": 3
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name1"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 7
				"gridX": 5
				"gridWidth": 3
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "修改"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 7
				"gridX": 9
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "课程信息："
				"icon": #SwingIcon0
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
				"gridX": 3
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name2"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 9
				"gridX": 5
				"gridWidth": 3
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset1"
				"text": "修改"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 9
				"gridX": 9
			} )
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 810, 580 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
