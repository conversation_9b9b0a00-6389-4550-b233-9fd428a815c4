/*
 * Created by <PERSON><PERSON>ormDesigner on Fri Jul 25 16:50:01 CST 2025
 */

package view;

import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.CourseDao;
import dao.TeacherDao;
import pojo.Course;
import pojo.Teacher;
import util.StringUtil;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * 添加课程窗口
 * <AUTHOR>
 */
public class AddCourse extends JFrame {
    private JComboBox<Teacher> cb_teacher;

    public AddCourse() {
        initComponents();
        initData();
        initEventHandlers();
    }

    private void initData() {
        // 加载教师列表
        TeacherDao teacherDao = new TeacherDao();
        List<Teacher> teacherList = teacherDao.getTeacherList(new Teacher());
        DefaultComboBoxModel<Teacher> teacherModel = new DefaultComboBoxModel<>();
        for (Teacher teacher : teacherList) {
            teacherModel.addElement(teacher);
        }
        cb_teacher.setModel(teacherModel);
    }

    private void initEventHandlers() {
        btn_sure.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String courseName = txt_name.getText().trim();
                String courseInfo = txt_info.getText().trim();
                String maxStudentStr = txt_maxStudent.getText().trim();
                Teacher selectedTeacher = (Teacher) cb_teacher.getSelectedItem();

                if (StringUtil.isEmpty(courseName)) {
                    JOptionPane.showMessageDialog(AddCourse.this, "课程名称不能为空！");
                    return;
                }

                if (StringUtil.isEmpty(courseInfo)) {
                    JOptionPane.showMessageDialog(AddCourse.this, "课程信息不能为空！");
                    return;
                }

                if (StringUtil.isEmpty(maxStudentStr)) {
                    JOptionPane.showMessageDialog(AddCourse.this, "最大学生数量不能为空！");
                    return;
                }

                if (selectedTeacher == null) {
                    JOptionPane.showMessageDialog(AddCourse.this, "请选择授课教师！");
                    return;
                }

                try {
                    int maxStudentNum = Integer.parseInt(maxStudentStr);
                    if (maxStudentNum <= 0) {
                        JOptionPane.showMessageDialog(AddCourse.this, "最大学生数量必须大于0！");
                        return;
                    }

                    Course course = new Course();
                    course.setName(courseName);
                    course.setInfo(courseInfo);
                    course.setTeacher_id(selectedTeacher.getId());
                    course.setMax_student_num(maxStudentNum);
                    course.setSelected_num(0); // 初始选课人数为0

                    CourseDao courseDao = new CourseDao();
                    int result = courseDao.addCourse(course);

                    if (result > 0) {
                        JOptionPane.showMessageDialog(AddCourse.this, "课程添加成功！");
                        resetValue();
                    } else {
                        JOptionPane.showMessageDialog(AddCourse.this, "课程添加失败！");
                    }
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(AddCourse.this, "最大学生数量必须是数字！");
                }
            }
        });

        btn_reset.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                resetValue();
            }
        });
    }

    private void resetValue() {
        txt_name.setText("");
        txt_info.setText("");
        txt_maxStudent.setText("");
        if (cb_teacher.getItemCount() > 0) {
            cb_teacher.setSelectedIndex(0);
        }
    }
    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        label2 = new JLabel();
        txt_info = new JTextField();
        label3 = new JLabel();
        cb_teacher = new JComboBox<>();
        label4 = new JLabel();
        txt_maxStudent = new JTextField();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("添加课程信息");
        setIconImage(new ImageIcon(getClass().getResource("/添加.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "93dlu, $lcgap, 104dlu, $lcgap, 81dlu",
            "5*(49dlu, $lgap), 58dlu"));

        //---- label1 ----
        label1.setText("课程名称：");
        label1.setIcon(new ImageIcon(getClass().getResource("/课程列表.png")));
        contentPane.add(label1, CC.xy(1, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(3, 1));

        //---- label2 ----
        label2.setText("课程信息：");
        label2.setIcon(new ImageIcon(getClass().getResource("/班级介绍.png")));
        contentPane.add(label2, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_info, CC.xy(3, 3));

        //---- label3 ----
        label3.setText("授课教师：");
        label3.setIcon(new ImageIcon(getClass().getResource("/老师.png")));
        contentPane.add(label3, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_teacher, CC.xy(3, 5));

        //---- label4 ----
        label4.setText("最大学生数：");
        label4.setIcon(new ImageIcon(getClass().getResource("/学生管理.png")));
        contentPane.add(label4, CC.xy(1, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_maxStudent, CC.xy(3, 7));

        //---- btn_sure ----
        btn_sure.setText("确认添加");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/确认.png")));
        contentPane.add(btn_sure, CC.xy(1, 9, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("重置表单");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/重置.png")));
        contentPane.add(btn_reset, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JLabel label2;
    private JTextField txt_info;
    private JLabel label3;
    private JLabel label4;
    private JTextField txt_maxStudent;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
