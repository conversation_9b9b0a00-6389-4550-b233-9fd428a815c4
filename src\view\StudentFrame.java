/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Jul 12 14:34:02 CST 2025
 */

package view;

import java.awt.*;
import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import pojo.Student;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * <AUTHOR>
 */
public class StudentFrame extends J<PERSON>rame {
    public static String userType;
    public static Student student;
    public StudentFrame(String mUserType, Student mStudent) {
        userType = mUserType;
        student= mStudent;

        initComponents();
        item_psw1.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //打开修改密码窗口，并设置为可见
                new EditPswFrame().setVisible(true);
            }
        });
        //item_quit:事件处理
        item_quit2.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //弹出一个确认对话框
                if(JOptionPane.showConfirmDialog(StudentFrame.this,"确认退出吗？")==JOptionPane.OK_OPTION)
                {
                    System.exit(0);
                }
            }
        });
        item_stuList.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new StuManageFrame().setVisible(true);
            }
        });
        item_course.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new CourseMangerFrame().setVisible(true);
            }
        });
        item_stugrademanage2.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new GradeManageFrame().setVisible(true);
            }
        });
        item_help2.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new AboutUsFrame().setVisible(true);
            }
        });
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        menuBar1 = new JMenuBar();
        menu1 = new JMenu();
        item_psw1 = new JMenuItem();
        item_quit2 = new JMenuItem();
        menu2 = new JMenu();
        item_stuList = new JMenuItem();
        menu3 = new JMenu();
        item_course = new JMenuItem();
        menu4 = new JMenu();
        item_stugrademanage2 = new JMenuItem();
        menu5 = new JMenu();
        item_help2 = new JMenuItem();
        panel1 = new JPanel();

        //======== this ========
        setTitle("\u5b66\u751f\u4fe1\u606f\u7cfb\u7edf");
        setIconImage(new ImageIcon(getClass().getResource("/\u5b66\u751f.png")).getImage());
        setResizable(false);
        setBackground(Color.blue);
        setForeground(SystemColor.textHighlightText);
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "1920px",
            "1020px"));

        //======== menuBar1 ========
        {

            //======== menu1 ========
            {
                menu1.setText("\u7cfb\u7edf\u8bbe\u7f6e");
                menu1.setIcon(new ImageIcon(getClass().getResource("/\u7cfb\u7edf\u8bbe\u7f6e.png")));

                //---- item_psw1 ----
                item_psw1.setText("\u4fee\u6539\u5bc6\u7801");
                item_psw1.setIcon(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")));
                menu1.add(item_psw1);

                //---- item_quit2 ----
                item_quit2.setText("\u9000\u51fa\u7cfb\u7edf");
                item_quit2.setIcon(new ImageIcon(getClass().getResource("/\u9000\u51fa.png")));
                menu1.add(item_quit2);
            }
            menuBar1.add(menu1);

            //======== menu2 ========
            {
                menu2.setText("\u5b66\u751f\u4fe1\u606f\u67e5\u8be2");
                menu2.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));

                //---- item_stuList ----
                item_stuList.setText("\u5b66\u751f\u5217\u8868");
                item_stuList.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u5217\u8868.png")));
                menu2.add(item_stuList);
            }
            menuBar1.add(menu2);

            //======== menu3 ========
            {
                menu3.setText("\u8bfe\u7a0b\u4fe1\u606f\u67e5\u8be2");
                menu3.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));

                //---- item_course ----
                item_course.setText("\u8bfe\u7a0b\u67e5\u8be2");
                item_course.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
                menu3.add(item_course);
            }
            menuBar1.add(menu3);

            //======== menu4 ========
            {
                menu4.setText("\u6210\u7ee9\u7ba1\u7406");
                menu4.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u5217\u8868.png")));

                //---- item_stugrademanage2 ----
                item_stugrademanage2.setText("\u6210\u7ee9\u67e5\u8be2");
                item_stugrademanage2.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u5217\u8868.png")));
                menu4.add(item_stugrademanage2);
            }
            menuBar1.add(menu4);

            //======== menu5 ========
            {
                menu5.setText("\u5e2e\u52a9");
                menu5.setIcon(new ImageIcon(getClass().getResource("/\u5e2e\u52a9.png")));

                //---- item_help2 ----
                item_help2.setText("\u5173\u4e8e\u6211\u4eec");
                item_help2.setIcon(new ImageIcon(getClass().getResource("/\u5173\u4e8e\u6211\u4eec.png")));
                menu5.add(item_help2);
            }
            menuBar1.add(menu5);
        }
        setJMenuBar(menuBar1);

        //======== panel1 ========
        {
            panel1.setBackground(new Color(0x009999));
            panel1.setLayout(new FormLayout(
                "default",
                "default"));
        }
        contentPane.add(panel1, CC.xy(1, 1, CC.FILL, CC.FILL));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JMenuBar menuBar1;
    private JMenu menu1;
    private JMenuItem item_psw1;
    private JMenuItem item_quit2;
    private JMenu menu2;
    private JMenuItem item_stuList;
    private JMenu menu3;
    private JMenuItem item_course;
    private JMenu menu4;
    private JMenuItem item_stugrademanage2;
    private JMenu menu5;
    private JMenuItem item_help2;
    private JPanel panel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
