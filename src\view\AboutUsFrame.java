/*
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on Mon Jul 07 16:05:37 CST 2025
 */

package view;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.net.URI;
import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;

/**
 * <AUTHOR>
 */
public class AboutUsFrame extends JFrame {
    public AboutUsFrame() {
        initComponents();
        // 迫不及待的按钮事件处理
        btn_sure.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                try {
                    Desktop.getDesktop().browse(new URI("http://programmer.ischoolbar.com"));
                } catch (Exception ex) {
                    JOptionPane.showMessageDialog(AboutUsFrame.this, "无法打开浏览器，请重试！！！");
                }
            }
        });
        btn_delay.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
            }
        });
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label2 = new JLabel();
        label3 = new JLabel();
        label4 = new JLabel();
        btn_sure = new JButton();
        btn_delay = new JButton();

        //======== this ========
        setTitle("\u5173\u4e8e\u6211\u4eec");
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "63dlu, $lcgap, 116dlu, $lcgap, 118dlu, $lcgap, 84dlu",
            "43dlu, $lgap, 34dlu, $lgap, 33dlu, $lgap, 47dlu"));

        //---- label2 ----
        label2.setText("\u3010\u733f\u6765\u5165\u6b64\u3011\u51fa\u54c1");
        label2.setIcon(new ImageIcon(getClass().getResource("/logo.png")));
        label2.setFont(label2.getFont().deriveFont(label2.getFont().getStyle() | Font.BOLD, label2.getFont().getSize() + 2f));
        contentPane.add(label2, CC.xywh(3, 1, 4, 1, CC.CENTER, CC.FILL));

        //---- label3 ----
        label3.setText("  \u7f51\u5740:http://programmer.ischoolbar.com");
        label3.setFont(label3.getFont().deriveFont(label3.getFont().getStyle() | Font.BOLD, label3.getFont().getSize() + 2f));
        contentPane.add(label3, CC.xywh(3, 3, 3, 1, CC.CENTER, CC.FILL));

        //---- label4 ----
        label4.setText("\u6bcf\u5929\u66f4\u65b0\u4e00\u4e2a\u9879\u76ee\uff0c\u5e76\u9644\u5e26\u89c6\u9891\u6559\u7a0b\uff01");
        label4.setFont(label4.getFont().deriveFont(label4.getFont().getStyle() | Font.BOLD, label4.getFont().getSize() + 2f));
        contentPane.add(label4, CC.xywh(3, 5, 3, 1, CC.CENTER, CC.DEFAULT));

        //---- btn_sure ----
        btn_sure.setText("\u8feb\u4e0d\u53ca\u5f85\u53bb\u770b\u770b\uff01");
        contentPane.add(btn_sure, CC.xy(3, 7));

        //---- btn_delay ----
        btn_delay.setText("\u5fc3\u60c5\u4e0d\u597d\u4ee5\u540e\u518d\u8bf4\uff01");
        contentPane.add(btn_delay, CC.xy(5, 7, CC.DEFAULT, CC.CENTER));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label2;
    private JLabel label3;
    private JLabel label4;
    private JButton btn_sure;
    private JButton btn_delay;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
