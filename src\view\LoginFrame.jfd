JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "62dlu, labelcompgap, 35dlu, labelcompgap, 124dlu, labelcompgap, 60dlu"
			"$rowSpecs": "50dlu, linegap, 33dlu, linegap, 33dlu, linegap, 33dlu, linegap, 38dlu"
		} ) {
			name: "this"
			"title": "登陆界面"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/用户名.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "学生信息系统登陆界面"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/logo.png" )
				"font": new com.jformdesigner.model.SwingDerivedFont( null, 1, 14, false )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridWidth": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints CENTER
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "用户名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/用户名.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "密码："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/密码.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "pf_psw"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "用户类型："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/userType.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_type"
				"model": new javax.swing.DefaultComboBoxModel {
					selectedItem: "系统管理员"
					addElement( "系统管理员" )
					addElement( "教师" )
					addElement( "学生" )
				}
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_login"
				"text": "登录"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/登录.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "重置"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 15 )
			"size": new java.awt.Dimension( 530, 360 )
		} )
	}
}
