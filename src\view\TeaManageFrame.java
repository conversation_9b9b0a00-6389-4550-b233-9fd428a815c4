package view;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.TeacherDao;
import pojo.Teacher;
import util.StringUtil;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Vector;

public class TeaManageFrame extends JFrame {
    private ButtonGroup group;
    //1、自动生成的初始化界面的代码，放到构造方法的第一行
    public TeaManageFrame() {
        initComponents();
        //2、三个单选按钮实现单选效果
        group = new ButtonGroup();
        group.add(rb_male);
        group.add(rb_female);
        // 设置表组件的显示模式
        DefaultTableModel dtm = new DefaultTableModel();
        dtm.setDataVector(new Object[][]{}, new String[]{"教师ID", "教师姓名", "教师性别", "教师职称", "教师年龄", "登录密码"});
        table1.setModel(dtm);

        // 查询按钮的ACtionEvent事件处理
        btn_query.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //获取用户从界面输入的姓名
                String teaName = txt_name.getText().toString();
                Teacher teacher = new Teacher();
                teacher.setName(teaName);
                setTable(teacher);
            }
        });

        // 选中表中某一项时，将该教师数据设置到页面下方的控件上
        table1.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                selectedTableRow();
            }
        });

        // btn_update事件处理，实现修改数据的功能
        btn_update.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //判断一下表中数据有没有被选中，如果没有选中数据，需提示用户先选中才能修改
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "请选中需要修改的数据");
                    return;
                }
            //从界面中获取用户修改过后的数据
                String teaName = txt_name2.getText().toString();
                String teaTitle = txt_title.getText().toString();
                String teaAge = txt_age.getText().toString();
                String teaPsw = pf_psw.getText().toString();
                String teaSex = rb_male.isSelected() ? "男" : "女";

                if(StringUtil.isEmpty(teaName)) {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "教师姓名不能为空");
                    return;
                }
                //把数据封装到Teacher中
                Teacher teacher = new Teacher();
                //从选中的行中获取教师编号
                teacher.setId(Integer.parseInt(table1.getValueAt(srow, 0).toString()));
                teacher.setName(teaName);
                teacher.setSex(teaSex);
                teacher.setTitle(teaTitle);
                teacher.setAge(Integer.parseInt(teaAge));
                teacher.setPassword(teaPsw);
                //dao.update
                TeacherDao dao = new TeacherDao();
                boolean flag = dao.update(teacher);
                //根据返回值提示用户不同的信息
                if(flag) {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "修改成功！");
                } else {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "修改失败，请重试！");
                }
                //及时更新表格组件中的数据
                setTable(new Teacher());
            }
        });

        // 删除按钮事件处理过程
        btn_delete.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "请先选中要删除的数据！");
                    return;
                }
                //从表中获取选中的那条数据的教师的编号，dao.delete()
                int teaId = Integer.parseInt(table1.getValueAt(srow, 0).toString());
                TeacherDao dao = new TeacherDao();
                boolean flag = dao.delete(teaId);
                //根据返回值不同执行不同的提示
                if(flag) {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "删除成功！");
                } else {
                    JOptionPane.showMessageDialog(TeaManageFrame.this, "删除失败，请重试！");
                }

                setTable(new Teacher());
            }
        });

        // 及时更新表中的数据
        setTable(new Teacher());
    }

    private void selectedTableRow() {
        //把选中的这一行数据的每个字段的字段值提取出来
        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        int srow = table1.getSelectedRow();
        String teaName = dtm.getValueAt(srow, 1).toString();
        String teaSex = dtm.getValueAt(srow, 2).toString();
        String teaTitle = dtm.getValueAt(srow, 3).toString();
        String teaAge = dtm.getValueAt(srow, 4).toString();
        String teaPsw = dtm.getValueAt(srow, 5).toString();
        //设置到界面
        txt_name2.setText(teaName);
        txt_title.setText(teaTitle);
        txt_age.setText(teaAge);
        pf_psw.setText(teaPsw);
        //把性别设置到单选按钮上
        group.clearSelection();
        if(teaSex.equals("男")) {
            rb_male.setSelected(true);
        } else {
            rb_female.setSelected(true);
        }
    }
    //dao查询，把查询到的结果设置到table中
    private void setTable(Teacher teacher) {
        //dao.getTeacherList（）
        TeacherDao dao = new TeacherDao();
        List<Teacher> teaList = dao.getTeacherList(teacher);

        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        dtm.setRowCount(0);

        for(Teacher t : teaList) {
            Vector v = new Vector();
            v.add(t.getId());
            v.add(t.getName());
            v.add(t.getSex());
            v.add(t.getTitle());
            v.add(t.getAge());
            v.add(t.getPassword());
            dtm.addRow(v);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label2 = new JLabel();
        txt_name2 = new JTextField();
        label3 = new JLabel();
        panel1 = new JPanel();
        rb_male = new JRadioButton();
        rb_female = new JRadioButton();
        label4 = new JLabel();
        txt_title = new JTextField();
        label5 = new JLabel();
        txt_age = new JTextField();
        label6 = new JLabel();
        pf_psw = new JTextField();
        btn_update = new JButton();
        btn_delete = new JButton();

        //======== this ========
        setTitle("\u6559\u5e08\u7ba1\u7406\u7a97\u53e3");
        setIconImage(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "53dlu, $lcgap, 81dlu, $lcgap, 88dlu, 2*($lcgap), 87dlu, 114dlu, $lcgap, 93dlu, $lcgap, 62dlu",
            "53dlu, $lgap, 183dlu, $lgap, 33dlu, $lgap, 39dlu, $lgap, 38dlu"));

        //---- label1 ----
        label1.setText("\u6559\u5e08\u59d3\u540d\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- btn_query ----
        btn_query.setText("\u67e5\u8be2");
        btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
        contentPane.add(btn_query, CC.xy(9, 1, CC.CENTER, CC.DEFAULT));

        //======== scrollPane1 ========
        {
            scrollPane1.setViewportView(table1);
        }
        contentPane.add(scrollPane1, CC.xywh(3, 3, 9, 1, CC.DEFAULT, CC.FILL));

        //---- label2 ----
        label2.setText("\u6559\u5e08\u59d3\u540d\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
        contentPane.add(label2, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name2, CC.xy(5, 5));

        //---- label3 ----
        label3.setText("\u6559\u5e08\u6027\u522b\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u6027\u522b.png")));
        contentPane.add(label3, CC.xy(9, 5, CC.RIGHT, CC.DEFAULT));

        //======== panel1 ========
        {
            panel1.setLayout(new FormLayout(
                "default, $lcgap, default",
                "default"));

            //---- rb_male ----
            rb_male.setText("\u7537");
            panel1.add(rb_male, CC.xy(1, 1, CC.CENTER, CC.DEFAULT));

            //---- rb_female ----
            rb_female.setText("\u5973");
            panel1.add(rb_female, CC.xy(3, 1));
        }
        contentPane.add(panel1, CC.xy(11, 5));

        //---- label4 ----
        label4.setText("\u6559\u5e08\u804c\u79f0\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/\u804c\u79f0\u8bc4\u5b9a.png")));
        contentPane.add(label4, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_title, CC.xy(5, 7));

        //---- label5 ----
        label5.setText("\u6559\u5e08\u5e74\u9f84\uff1a");
        label5.setIcon(new ImageIcon(getClass().getResource("/\u5e74\u9f84.png")));
        contentPane.add(label5, CC.xy(9, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_age, CC.xy(11, 7));

        //---- label6 ----
        label6.setText("\u767b\u9646\u5bc6\u7801\uff1a");
        label6.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label6, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_psw, CC.xy(5, 9));

        //---- btn_update ----
        btn_update.setText("\u786e\u8ba4\u4fee\u6539");
        btn_update.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_update, CC.xy(9, 9, CC.CENTER, CC.DEFAULT));

        //---- btn_delete ----
        btn_delete.setText("\u5220\u9664\u4fe1\u606f");
        btn_delete.setIcon(new ImageIcon(getClass().getResource("/\u5220\u9664.png")));
        contentPane.add(btn_delete, CC.xy(11, 9, CC.CENTER, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label2;
    private JTextField txt_name2;
    private JLabel label3;
    private JPanel panel1;
    private JRadioButton rb_male;
    private JRadioButton rb_female;
    private JLabel label4;
    private JTextField txt_title;
    private JLabel label5;
    private JTextField txt_age;
    private JLabel label6;
    private JTextField pf_psw;
    private JButton btn_update;
    private JButton btn_delete;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
