package view;

import com.jgoodies.forms.factories.CC;
import com.jgoodies.forms.layout.FormLayout;
import dao.ScoreDao;
import dao.StudentDao;
import pojo.Score;
import pojo.Student;
import util.StringUtil;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.List;
import java.util.Vector;

public class GradeManageFrame extends JFrame {
    private List<Score> scoreList; // 成绩列表

    public GradeManageFrame() {
        initComponents();

        // 设置表组件的显示模式
        DefaultTableModel dtm = new DefaultTableModel();
        dtm.setDataVector(new Object[][]{}, new String[]{"成绩编号", "学生姓名", "课程名称", "成绩"});
        table1.setModel(dtm);
        // 初始化事件处理器
        initEventHandlers();
        //初始数据
        setTable(new Score());

    }

    private void initEventHandlers() {
        // 查询按钮的ACtionEvent事件处理
        btn_query.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String studentName = txt_name.getText().toString();
                Score score = new Score();
                score.setStudentName(studentName);
                //dao查询，把查询到的结果设置到table中
                setTable(score);
            }
        });

        // 选中表中某一项时，将该班级数据设置到页面下方的控件上
        table1.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                super.mouseClicked(e);
                selectedTableRow();
            }
        });

        // btn_update事件处理
        btn_update.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "请先选中一条数据再进行修改！");
                    return;
                }
                //从界面中获取用户修改过后的数据
                String studentName = txt_name2.getText().toString();
                String scoreStr = txt_grade.getText().toString();

                if(StringUtil.isEmpty(studentName)) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "学生姓名不能为空！");
                    return;
                }
                if(StringUtil.isEmpty(scoreStr)) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "成绩不能为空！");
                    return;
                }

                try {
                    int scoreValue = Integer.parseInt(scoreStr);
                    if(scoreValue < 0 || scoreValue > 100) {
                        JOptionPane.showMessageDialog(GradeManageFrame.this, "成绩必须在0-100之间！");
                        return;
                    }

                    Score score = new Score();
                    score.setId(Integer.parseInt(table1.getValueAt(srow, 0).toString()));
                    score.setScore(scoreValue);

                    ScoreDao dao = new ScoreDao();
                    Score existingScore = dao.getScoreById(score.getId());
                    if(existingScore != null) {
                        score.setStudentId(existingScore.getStudentId());
                        score.setCourseId(existingScore.getCourseId());

                        // 检查学生名字是否有变化，如果有变化则更新学生表
                        if(!studentName.equals(existingScore.getStudentName())) {
                            // 更新学生表中的名字
                            StudentDao studentDao = new StudentDao();
                            Student student = new Student();
                            student.setId(existingScore.getStudentId());
                            student.setName(studentName);

                            // 获取学生的其他信息以便完整更新
                            Student existingStudent = studentDao.getStudentById(existingScore.getStudentId());
                            if(existingStudent != null) {
                                student.setClassId(existingStudent.getClassId());
                                student.setSex(existingStudent.getSex());
                                student.setPassword(existingStudent.getPassword());

                                boolean studentUpdateFlag = studentDao.update(student);
                                if(!studentUpdateFlag) {
                                    JOptionPane.showMessageDialog(GradeManageFrame.this, "学生名字更新失败！");
                                    return;
                                }
                            } else {
                                JOptionPane.showMessageDialog(GradeManageFrame.this, "未找到学生信息！");
                                return;
                            }
                        }

                        // 更新成绩
                        boolean flag = dao.update(score);
                        //根据返回值提示用户不同的信息
                        if(flag) {
                            JOptionPane.showMessageDialog(GradeManageFrame.this, "恭喜您，修改成功！");
                        } else {
                            JOptionPane.showMessageDialog(GradeManageFrame.this, "修改失败！！！");
                        }
                        //及时更新表格组件中的数据
                        setTable(new Score());
                    } else {
                        JOptionPane.showMessageDialog(GradeManageFrame.this, "未找到该成绩记录！");
                    }
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "成绩必须是数字！");
                }
            }
        });

        btn_delete.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //1、判断用户有没有选中表中的数据，没有则提示
                int srow = table1.getSelectedRow();
                if(srow == -1) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "请先选中要删除的数据");
                    return;
                }
                //2、从表中获取选中的那条数据的成绩编号，dao.delete()
                int scoreId = Integer.parseInt(table1.getValueAt(srow, 0).toString());

                ScoreDao dao = new ScoreDao();
                boolean flag = dao.delete(scoreId);
                //3、根据返回值不同执行不同的提示
                if(flag) {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "删除成功！");
                } else {
                    JOptionPane.showMessageDialog(GradeManageFrame.this, "删除失败，请重试！");
                }
                //4、及时更新表中的数据
                setTable(new Score());
            }
        });
    }

    // 选中行显示详情
    private void selectedTableRow() {
        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        int srow = table1.getSelectedRow();
        String studentName = dtm.getValueAt(srow, 1).toString();
        String score = dtm.getValueAt(srow, 3).toString();

        txt_name2.setText(studentName);
        txt_grade.setText(score);
    }

    //选中表中某一项时，将成绩数据设置到页面下方的控件上
    private void setTable(Score score) {
        ScoreDao dao = new ScoreDao();
        scoreList = dao.getScoreList(score);
        //把选中的这一行数据的每个字段的字段值提取出来
        DefaultTableModel dtm = (DefaultTableModel)table1.getModel();
        dtm.setRowCount(0);

        for(Score scoreItem : scoreList) {
            Vector v = new Vector();
            v.add(scoreItem.getId());
            v.add(scoreItem.getStudentName());
            v.add(scoreItem.getCourseName());
            v.add(scoreItem.getScore());
            dtm.addRow(v);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label2 = new JLabel();
        txt_name2 = new JTextField();
        btn_update = new JButton();
        label3 = new JLabel();
        txt_grade = new JTextField();
        btn_delete = new JButton();

        //======== this ========
        setTitle("\u6210\u7ee9\u4fe1\u606f\u7ba1\u7406");
        setIconImage(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "56dlu, $lcgap, 62dlu, $lcgap, 191dlu, $lcgap, 47dlu, $lcgap, 57dlu",
            "62dlu, $lgap, 138dlu, $lgap, 48dlu, $lgap, 56dlu"));

        //---- label1 ----
        label1.setText("\u5b66\u751f\u540d\u79f0\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- btn_query ----
        btn_query.setText("\u67e5\u8be2");
        btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
        contentPane.add(btn_query, CC.xy(7, 1));

        //======== scrollPane1 ========
        {
            scrollPane1.setViewportView(table1);
        }
        contentPane.add(scrollPane1, CC.xywh(3, 3, 5, 1, CC.FILL, CC.FILL));

        //---- label2 ----
        label2.setText("\u5b66\u751f\u540d\u79f0\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u540d\u79f0.png")));
        contentPane.add(label2, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name2, CC.xy(5, 5));

        //---- btn_update ----
        btn_update.setText("\u786e\u8ba4\u4fee\u6539");
        btn_update.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_update, CC.xy(7, 5));

        //---- label3 ----
        label3.setText("\u6210\u7ee9\u4fe1\u606f\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u804c\u79f0\u8bc4\u5b9a.png")));
        contentPane.add(label3, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_grade, CC.xy(5, 7));

        //---- btn_delete ----
        btn_delete.setText("\u5220\u9664");
        btn_delete.setIcon(new ImageIcon(getClass().getResource("/\u5220\u9664.png")));
        contentPane.add(btn_delete, CC.xy(7, 7));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label2;
    private JTextField txt_name2;
    private JButton btn_update;
    private JLabel label3;
    private JTextField txt_grade;
    private JButton btn_delete;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
