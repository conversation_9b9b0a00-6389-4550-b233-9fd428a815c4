JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "25dlu, labelcompgap, 75dlu, labelcompgap, 75dlu, labelcompgap, 75dlu, labelcompgap, 82dlu, labelcompgap, 75dlu, labelcompgap, 25dlu"
			"$rowSpecs": "41dlu, linegap, 120dlu, linegap, 41dlu, linegap, 41dlu"
		} ) {
			name: "this"
			"title": "学生列表窗口"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "学生姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "所属班级："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_class"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_query"
				"text": "查询"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 11
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "scrollPane1"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "table1"
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"gridWidth": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "学生姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name2"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "学生性别："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/性别.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
				"$columnSpecs": "default, labelcompgap, default, labelcompgap, default"
				"$rowSpecs": "default"
			} ) {
				name: "panel1"
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_male"
					"text": "男"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 1
					"gridY": 1
				} )
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_female"
					"text": "女"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 3
					"gridY": 1
				} )
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_secret"
					"text": "保密"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 5
					"gridY": 1
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_update"
				"text": "确认修改"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 11
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label5"
				"text": "所属班级："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_class2"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label6"
				"text": "登陆密码："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/密码.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "pf_psw"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_delete"
				"text": "删除学生"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/删除.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 11
				"gridY": 7
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 15 )
			"size": new java.awt.Dimension( 820, 430 )
		} )
	}
}
