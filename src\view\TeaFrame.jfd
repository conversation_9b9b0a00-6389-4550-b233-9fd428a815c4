JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "1920"
			"$rowSpecs": "1020"
		} ) {
			name: "this"
			"title": "教师信息系统"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/学生.png" )
			"resizable": false
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
				"$columnSpecs": "default"
				"$rowSpecs": "default"
			} ) {
				name: "panel1"
				"background": new java.awt.Color( 51, 255, 204, 255 )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			menuBar: new FormContainer( "javax.swing.JMenuBar", new FormLayoutManager( class javax.swing.JMenuBar ) ) {
				name: "menuBar1"
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu1"
					"text": "系统设置"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/系统设置.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_psw1"
						"text": "修改密码"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/修改密码.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_quit1"
						"text": "退出系统"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/退出.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu7"
					"text": "学生管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_addStu"
						"text": "添加学生"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_stuList"
						"text": "学生列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/用户列表.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu6"
					"text": "班级管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级管理.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_addClass"
						"text": "添加班级"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_classList"
						"text": "班级列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级列表.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu2"
					"text": "教师信息查询"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_teaList"
						"text": "教师列表"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu3"
					"text": "课程信息查询"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/课程列表.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_class"
						"text": "课程查询"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu4"
					"text": "成绩管理"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级列表.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_stugrade"
						"text": "成绩输入"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
					} )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_stugrademanage"
						"text": "成绩管理"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/用户列表.png" )
					} )
				} )
				add( new FormContainer( "javax.swing.JMenu", new FormLayoutManager( class javax.swing.JMenu ) ) {
					name: "menu5"
					"text": "帮助"
					"icon": new com.jformdesigner.model.SwingIcon( 0, "/帮助.png" )
					add( new FormComponent( "javax.swing.JMenuItem" ) {
						name: "item_help"
						"text": "关于我们"
						"icon": new com.jformdesigner.model.SwingIcon( 0, "/关于我们.png" )
					} )
				} )
			}
		}, new FormLayoutConstraints( null ) {
			"size": new java.awt.Dimension( 590, 455 )
			"location": new java.awt.Point( 0, 0 )
		} )
	}
}
