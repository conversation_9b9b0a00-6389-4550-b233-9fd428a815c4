/*
 * Created by <PERSON><PERSON><PERSON><PERSON>esigner on Sat Jul 12 14:44:28 CST 2025
 */

package view;

import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;

/**
 * <AUTHOR>
 */
public class TeaClassFrame extends JFrame {
    private ButtonGroup group;
    public TeaClassFrame() {

        initComponents();
        group = new ButtonGroup();
        group.add(rb_male);
        group.add(rb_female);
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label2 = new JLabel();
        txt_name2 = new JTextField();
        label3 = new JLabel();
        panel1 = new JPanel();
        rb_male = new JRadioButton();
        rb_female = new JRadioButton();
        label4 = new JLabel();
        txt_title = new JTextField();
        label5 = new JLabel();
        txt_age = new JTextField();
        label6 = new JLabel();
        pf_psw = new JTextField();
        btn_update = new JButton();
        btn_delete = new JButton();

        //======== this ========
        setTitle("\u6559\u5e08\u4fe1\u606f\u7ba1\u7406");
        setIconImage(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "55dlu, $lcgap, 91dlu, $lcgap, 88dlu, $lcgap, 71dlu, $lcgap, 68dlu, $lcgap, 55dlu",
            "48dlu, $lgap, 154dlu, $lgap, 42dlu, $lgap, 39dlu, $lgap, 41dlu"));

        //---- label1 ----
        label1.setText("\u6559\u5e08\u59d3\u540d\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- btn_query ----
        btn_query.setText("\u67e5\u8be2");
        btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
        contentPane.add(btn_query, CC.xy(9, 1));

        //======== scrollPane1 ========
        {
            scrollPane1.setViewportView(table1);
        }
        contentPane.add(scrollPane1, CC.xywh(3, 3, 7, 1, CC.FILL, CC.FILL));

        //---- label2 ----
        label2.setText("\u6559\u5e08\u59d3\u540d\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
        contentPane.add(label2, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name2, CC.xy(5, 5));

        //---- label3 ----
        label3.setText("\u6559\u5e08\u6027\u522b\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u6027\u522b.png")));
        contentPane.add(label3, CC.xy(7, 5, CC.RIGHT, CC.DEFAULT));

        //======== panel1 ========
        {
            panel1.setLayout(new FormLayout(
                "default, $lcgap, default",
                "default"));

            //---- rb_male ----
            rb_male.setText("\u7537");
            panel1.add(rb_male, CC.xy(1, 1));

            //---- rb_female ----
            rb_female.setText("\u5973");
            panel1.add(rb_female, CC.xy(3, 1));
        }
        contentPane.add(panel1, CC.xy(9, 5));

        //---- label4 ----
        label4.setText("\u6559\u5e08\u804c\u79f0\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/\u804c\u79f0\u8bc4\u5b9a.png")));
        contentPane.add(label4, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_title, CC.xy(5, 7));

        //---- label5 ----
        label5.setText("\u6559\u5e08\u5e74\u9f84\uff1a");
        label5.setIcon(new ImageIcon(getClass().getResource("/\u5e74\u9f84.png")));
        contentPane.add(label5, CC.xy(7, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_age, CC.xy(9, 7));

        //---- label6 ----
        label6.setText("\u767b\u5f55\u5bc6\u7801\uff1a");
        label6.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label6, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_psw, CC.xy(5, 9));

        //---- btn_update ----
        btn_update.setText("\u786e\u8ba4\u4fee\u6539");
        btn_update.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_update, CC.xy(7, 9));

        //---- btn_delete ----
        btn_delete.setText("\u5220\u9664\u4fe1\u606f");
        btn_delete.setIcon(new ImageIcon(getClass().getResource("/\u5220\u9664.png")));
        contentPane.add(btn_delete, CC.xy(9, 9));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label2;
    private JTextField txt_name2;
    private JLabel label3;
    private JPanel panel1;
    private JRadioButton rb_male;
    private JRadioButton rb_female;
    private JLabel label4;
    private JTextField txt_title;
    private JLabel label5;
    private JTextField txt_age;
    private JLabel label6;
    private JTextField pf_psw;
    private JButton btn_update;
    private JButton btn_delete;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
