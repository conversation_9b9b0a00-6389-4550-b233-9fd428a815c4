JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "63dlu, labelcompgap, 116dlu, labelcompgap, 118dlu, labelcompgap, 84dlu"
			"$rowSpecs": "43dlu, linegap, 34dlu, linegap, 33dlu, linegap, 47dlu"
		} ) {
			name: "this"
			"title": "关于我们"
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "【猿来入此】出品"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/logo.png" )
				"font": new com.jformdesigner.model.SwingDerivedFont( null, 1, 2, false )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridWidth": 4
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints CENTER
				"gridX": 3
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "  网址:http://programmer.ischoolbar.com"
				"font": new com.jformdesigner.model.SwingDerivedFont( null, 1, 2, false )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints CENTER
				"gridWidth": 3
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"gridY": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "每天更新一个项目，并附带视频教程！"
				"font": new com.jformdesigner.model.SwingDerivedFont( null, 1, 2, false )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints CENTER
				"gridWidth": 3
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_sure"
				"text": "迫不及待去看看！"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_delay"
				"text": "心情不好以后再说！"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints CENTER
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 15, 0 )
			"size": new java.awt.Dimension( 710, 305 )
		} )
	}
}

