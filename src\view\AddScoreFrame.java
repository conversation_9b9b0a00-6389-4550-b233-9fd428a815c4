/*
 * Created by <PERSON><PERSON>ormDesigner on Sat Jul 26 18:57:03 CST 2025
 */

package view;

import com.jgoodies.forms.factories.CC;
import com.jgoodies.forms.layout.FormLayout;
import dao.ScoreDao;
import dao.StudentDao;
import dao.CourseDao;
import pojo.Score;
import pojo.Student;
import pojo.Course;
import util.StringUtil;

import javax.swing.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * 成绩输入窗口
 * <AUTHOR>
 */
public class AddScoreFrame extends JFrame {
    public AddScoreFrame() {
        initComponents();
        initData();
        initEventHandlers();
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        cb_student = new JComboBox();
        label2 = new JLabel();
        cb_course = new JComboBox();
        label3 = new JLabel();
        txt_score = new JTextField();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u6210\u7ee9\u8f93\u5165");
        setIconImage(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "93dlu, $lcgap, 104dlu, $lcgap, 81dlu",
            "3*(49dlu, $lgap), 58dlu"));

        //---- label1 ----
        label1.setText("\u9009\u62e9\u5b66\u751f\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));
        contentPane.add(label1, CC.xy(1, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_student, CC.xy(3, 1));

        //---- label2 ----
        label2.setText("\u9009\u62e9\u8bfe\u7a0b\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));
        contentPane.add(label2, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(cb_course, CC.xy(3, 3));

        //---- label3 ----
        label3.setText("\u8f93\u5165\u6210\u7ee9\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u4ecb\u7ecd.png")));
        contentPane.add(label3, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_score, CC.xy(3, 5));

        //---- btn_sure ----
        btn_sure.setText("\u786e\u8ba4\u6dfb\u52a0");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_sure, CC.xy(1, 7, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e\u8868\u5355");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    private void initData() {
        // 加载学生列表
        StudentDao studentDao = new StudentDao();
        List<Student> studentList = studentDao.getStudentList(new Student());
        DefaultComboBoxModel<Student> studentModel = new DefaultComboBoxModel<>();
        for (Student student : studentList) {
            studentModel.addElement(student);
        }
        cb_student.setModel(studentModel);
        
        // 加载课程列表
        CourseDao courseDao = new CourseDao();
        List<Course> courseList = courseDao.getAllCourses();
        DefaultComboBoxModel<Course> courseModel = new DefaultComboBoxModel<>();
        for (Course course : courseList) {
            courseModel.addElement(course);
        }
        cb_course.setModel(courseModel);
    }
    
    private void initEventHandlers() {
        btn_sure.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                Student selectedStudent = (Student) cb_student.getSelectedItem();
                Course selectedCourse = (Course) cb_course.getSelectedItem();
                String scoreStr = txt_score.getText().trim();

                if (selectedStudent == null) {
                    JOptionPane.showMessageDialog(AddScoreFrame.this, "请选择学生！");
                    return;
                }
                
                if (selectedCourse == null) {
                    JOptionPane.showMessageDialog(AddScoreFrame.this, "请选择课程！");
                    return;
                }

                if (StringUtil.isEmpty(scoreStr)) {
                    JOptionPane.showMessageDialog(AddScoreFrame.this, "成绩不能为空！");
                    return;
                }
                
                try {
                    int scoreValue = Integer.parseInt(scoreStr);
                    if (scoreValue < 0 || scoreValue > 100) {
                        JOptionPane.showMessageDialog(AddScoreFrame.this, "成绩必须在0-100之间！");
                        return;
                    }
                    
                    Score score = new Score();
                    score.setStudentId(selectedStudent.getId());
                    score.setCourseId(selectedCourse.getId());
                    score.setScore(scoreValue);
                    
                    ScoreDao scoreDao = new ScoreDao();
                    boolean result = scoreDao.addScore(score);
                    
                    if (result) {
                        JOptionPane.showMessageDialog(AddScoreFrame.this, "成绩添加成功！");
                        resetValue();
                    } else {
                        JOptionPane.showMessageDialog(AddScoreFrame.this, "成绩添加失败！");
                    }
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(AddScoreFrame.this, "成绩必须是数字！");
                }
            }
        });

        btn_reset.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                resetValue();
            }
        });
    }

    private void resetValue() {
        if (cb_student.getItemCount() > 0) {
            cb_student.setSelectedIndex(0);
        }
        if (cb_course.getItemCount() > 0) {
            cb_course.setSelectedIndex(0);
        }
        txt_score.setText("");
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JComboBox cb_student;
    private JLabel label2;
    private JComboBox cb_course;
    private JLabel label3;
    private JTextField txt_score;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
