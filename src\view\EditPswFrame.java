/*
 * Created by <PERSON><PERSON>ormDesigner on Sun Jun 29 17:43:56 CST 2025
 */

package view;

import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.AdminDao;
import pojo.Admin;
import pojo.Student;
import pojo.Teacher;
import util.StringUtil;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * <AUTHOR>
 */
public class EditPswFrame extends JFrame {
    public EditPswFrame() {
        initComponents();
        //1、设置标签显示内容label_user
        if (MainFrame.userType != null && MainFrame.userType.equals("系统管理员")) {
                Admin admin = MainFrame.admin;
                label_user.setText("【系统管理员】" + admin.getName());
            } else if (TeaFrame.userType != null && TeaFrame.userType.equals("教师")) {
                Teacher teacher = TeaFrame.teacher;
                label_user.setText("【教师】" + teacher.getName());
            } else if (StudentFrame.userType != null && StudentFrame.userType.equals("学生")) {
                Student student = StudentFrame.student;
                label_user.setText("【学生】" + student.getName());
            }
        //2、btn_sure的事件处理
        btn_sure.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //动态效果
                //（1）从界面上获取用户输入的数据
                String oldPsw=pf_old.getText().toString();
                String newPsw1=pf_new1.getText().toString();
                String newPsw2=pf_new2.getText().toString();
                if(StringUtil.isEmpty(oldPsw))
                {
                    JOptionPane.showMessageDialog(EditPswFrame.this,"原密码不能为空，请输入原密码");
                    return;
                }

                if(StringUtil.isEmpty(newPsw1))
                {
                    JOptionPane.showMessageDialog(EditPswFrame.this,"新密码不能为空，请输入新密码");
                    return;
                }
                if(StringUtil.isEmpty(newPsw2))
                {
                    JOptionPane.showMessageDialog(EditPswFrame.this,"确认密码不能为空");
                    return;
                }
                if(!newPsw1.equals(newPsw2))
                {
                    JOptionPane.showMessageDialog(EditPswFrame.this,"新密码和确认密码不一致，请重新输入");
                    return;
                }
                //（2）dao.editpsw()
                AdminDao dao=new AdminDao();
                Admin admin=MainFrame.admin;
                admin.setPassword(oldPsw);
                String str= dao.editPassword(admin,newPsw1);
                JOptionPane.showMessageDialog(EditPswFrame.this,str);
            }
        });
        //3、btn_reset的事件处理
        btn_reset.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //清空界面
                pf_old.setText("");
                pf_new1.setText("");
                pf_new2.setText("");
            }
        });
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        IUser = new JLabel();
        label_user = new JLabel();
        Ipsw = new JLabel();
        pf_old = new JTextField();
        label2 = new JLabel();
        pf_new1 = new JTextField();
        label3 = new JLabel();
        pf_new2 = new JTextField();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u4fee\u6539\u5bc6\u7801\u7a97\u53e3");
        setIconImage(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "2*(109dlu, $lcgap), 60dlu",
            "50dlu, 2*($lgap, 30dlu), $lgap, 38dlu, $lgap, 42dlu"));

        //---- IUser ----
        IUser.setText("\u5f53\u524d\u7528\u6237\uff1a");
        IUser.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u540d.png")));
        contentPane.add(IUser, CC.xy(1, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(label_user, CC.xy(3, 1));

        //---- Ipsw ----
        Ipsw.setText("\u539f\u5bc6\u7801\uff1a");
        Ipsw.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(Ipsw, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_old, CC.xy(3, 3));

        //---- label2 ----
        label2.setText("\u65b0\u5bc6\u7801\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")));
        contentPane.add(label2, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_new1, CC.xy(3, 5));

        //---- label3 ----
        label3.setText("\u786e\u8ba4\u5bc6\u7801\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")));
        contentPane.add(label3, CC.xy(1, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_new2, CC.xy(3, 7));

        //---- btn_sure ----
        btn_sure.setText("\u786e\u8ba4");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_sure, CC.xy(1, 9, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel IUser;
    private JLabel label_user;
    private JLabel Ipsw;
    private JTextField pf_old;
    private JLabel label2;
    private JTextField pf_new1;
    private JLabel label3;
    private JTextField pf_new2;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
