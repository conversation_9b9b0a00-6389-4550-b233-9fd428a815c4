/*
 * Created by <PERSON><PERSON>ormDesigner on Sun Jun 29 16:39:26 CST 2025
 */

package view;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import pojo.Admin;
import pojo.Student;
import pojo.Teacher;

/**
 * <AUTHOR>
 */
public class MainFrame extends JFrame {
    //把登录窗口上的用户类型和用户对象注入到主界面中
    //（1）定义成员变量，用来接收用户从窗口传递过来的用户类型和用户对象
    public static String userType;
    public static Admin admin;
    //（2）编写构造方法完成对象的注入
    public MainFrame(String mUserType,Admin mAdmin) {
        userType=mUserType;
        admin=mAdmin;
        initComponents();
        //item_psw设置ActionEvent事件的处理，事件源;item_psw,监听器：匿名类对象
        item_psw.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                //打开修改密码窗口，并设置为可见
                new EditPswFrame().setVisible(true);
            }
        });
        //item_quit:事件处理
        item_quit.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //弹出一个确认对话框
                if(JOptionPane.showConfirmDialog(MainFrame.this,"确认退出吗？")==JOptionPane.OK_OPTION)
                {
                    System.exit(0);
                }
            }
        });
        //item_addStu事件处理
        item_addStu.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开添加学生窗口并可见
                new AddStuFrame().setVisible(true);
            }
        });
        //item_stuList：事件处理    事件源：item_stuList    监听器：匿名类对象
        item_stuList.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开学生列表窗口，并设置为可见
                new StuManageFrame().setVisible(true);
            }
        });
        //item_addClass事件处理
        item_addClass.addActionListener(new ActionListener() {


            @Override
            public void actionPerformed(ActionEvent e) {
                //打开添加班级窗口，并设置为可见
                new AddClassFrame().setVisible(true);
            }
        });
        item_classList.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new ClassManageFrame().setVisible(true);
            }
        });
        //item_addTea事件处理
        item_addTea.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new AddTeaFrame().setVisible(true);
            }
        });

        item_teaList.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new TeaManageFrame().setVisible(true);
            }
        });

        item_grade.addActionListener(new ActionListener() {
            //添加学生成绩
            @Override
            public void actionPerformed(ActionEvent e) {
                new AddScoreFrame().setVisible(true);
            }
        });

        item_grademanage.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //打开学生成绩窗口并可见
                new GradeManageFrame().setVisible(true);
            }
        });
        item_course.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                new CourseMangerFrame().setVisible(true);
            }
        });
        item_aboutUs.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                new AboutUsFrame().setVisible(true);
            }
        });


    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        menuBar1 = new JMenuBar();
        menu1 = new JMenu();
        item_psw = new JMenuItem();
        item_quit = new JMenuItem();
        menu2 = new JMenu();
        item_addStu = new JMenuItem();
        item_stuList = new JMenuItem();
        menu4 = new JMenu();
        item_addClass = new JMenuItem();
        item_classList = new JMenuItem();
        menu5 = new JMenu();
        item_addTea = new JMenuItem();
        item_teaList = new JMenuItem();
        menu7 = new JMenu();
        item_grade = new JMenuItem();
        item_grademanage = new JMenuItem();
        menu8 = new JMenu();
        item_course = new JMenuItem();
        menu6 = new JMenu();
        item_aboutUs = new JMenuItem();
        menu3 = new JMenu();
        panel1 = new JPanel();

        //======== this ========
        setTitle("\u5b66\u751f\u7ba1\u7406\u7cfb\u7edf\u4e3b\u754c\u9762");
        setIconImage(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "2560px",
            "1600px"));

        //======== menuBar1 ========
        {

            //======== menu1 ========
            {
                menu1.setText("\u7cfb\u7edf\u8bbe\u7f6e");
                menu1.setIcon(new ImageIcon(getClass().getResource("/\u7cfb\u7edf\u8bbe\u7f6e.png")));

                //---- item_psw ----
                item_psw.setText("\u4fee\u6539\u5bc6\u7801");
                item_psw.setIcon(new ImageIcon(getClass().getResource("/\u4fee\u6539\u5bc6\u7801.png")));
                menu1.add(item_psw);

                //---- item_quit ----
                item_quit.setText("\u9000\u51fa\u7cfb\u7edf");
                item_quit.setIcon(new ImageIcon(getClass().getResource("/\u9000\u51fa.png")));
                menu1.add(item_quit);
            }
            menuBar1.add(menu1);

            //======== menu2 ========
            {
                menu2.setText("\u5b66\u751f\u7ba1\u7406");
                menu2.setIcon(new ImageIcon(getClass().getResource("/\u5b66\u751f\u7ba1\u7406.png")));

                //---- item_addStu ----
                item_addStu.setText("\u6dfb\u52a0\u5b66\u751f");
                item_addStu.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu2.add(item_addStu);

                //---- item_stuList ----
                item_stuList.setText("\u5b66\u751f\u5217\u8868");
                item_stuList.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u5217\u8868.png")));
                menu2.add(item_stuList);
            }
            menuBar1.add(menu2);

            //======== menu4 ========
            {
                menu4.setText("\u73ed\u7ea7\u7ba1\u7406");
                menu4.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u7ba1\u7406.png")));

                //---- item_addClass ----
                item_addClass.setText("\u6dfb\u52a0\u73ed\u7ea7");
                item_addClass.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu4.add(item_addClass);

                //---- item_classList ----
                item_classList.setText("\u73ed\u7ea7\u5217\u8868");
                item_classList.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u5217\u8868.png")));
                menu4.add(item_classList);
            }
            menuBar1.add(menu4);

            //======== menu5 ========
            {
                menu5.setText("\u6559\u5e08\u7ba1\u7406");
                menu5.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));

                //---- item_addTea ----
                item_addTea.setText("\u6dfb\u52a0\u6559\u5e08");
                item_addTea.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu5.add(item_addTea);

                //---- item_teaList ----
                item_teaList.setText("\u6559\u5e08\u5217\u8868");
                item_teaList.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
                menu5.add(item_teaList);
            }
            menuBar1.add(menu5);

            //======== menu7 ========
            {
                menu7.setText("\u6210\u7ee9\u7ba1\u7406");
                menu7.setIcon(new ImageIcon(getClass().getResource("/\u73ed\u7ea7\u5217\u8868.png")));

                //---- item_grade ----
                item_grade.setText("\u6dfb\u52a0\u6210\u7ee9");
                item_grade.setIcon(new ImageIcon(getClass().getResource("/\u6dfb\u52a0.png")));
                menu7.add(item_grade);

                //---- item_grademanage ----
                item_grademanage.setText("\u6210\u7ee9\u67e5\u8be2");
                item_grademanage.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
                menu7.add(item_grademanage);
            }
            menuBar1.add(menu7);

            //======== menu8 ========
            {
                menu8.setText("\u8bfe\u7a0b\u4fe1\u606f\u67e5\u8be2");
                menu8.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));

                //---- item_course ----
                item_course.setText("\u8bfe\u7a0b\u67e5\u8be2");
                item_course.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
                menu8.add(item_course);
            }
            menuBar1.add(menu8);

            //======== menu6 ========
            {
                menu6.setText("\u5e2e\u52a9");
                menu6.setIcon(new ImageIcon(getClass().getResource("/\u5e2e\u52a9.png")));

                //---- item_aboutUs ----
                item_aboutUs.setText("\u5173\u4e8e\u6211\u4eec");
                item_aboutUs.setIcon(new ImageIcon(getClass().getResource("/\u5173\u4e8e\u6211\u4eec.png")));
                menu6.add(item_aboutUs);
            }
            menuBar1.add(menu6);
            menuBar1.add(menu3);
        }
        setJMenuBar(menuBar1);

        //======== panel1 ========
        {
            panel1.setBackground(new Color(0x002672));
            panel1.setLayout(new FormLayout(
                "default",
                "default"));
        }
        contentPane.add(panel1, CC.xy(1, 1, CC.FILL, CC.FILL));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JMenuBar menuBar1;
    private JMenu menu1;
    private JMenuItem item_psw;
    private JMenuItem item_quit;
    private JMenu menu2;
    private JMenuItem item_addStu;
    private JMenuItem item_stuList;
    private JMenu menu4;
    private JMenuItem item_addClass;
    private JMenuItem item_classList;
    private JMenu menu5;
    private JMenuItem item_addTea;
    private JMenuItem item_teaList;
    private JMenu menu7;
    private JMenuItem item_grade;
    private JMenuItem item_grademanage;
    private JMenu menu8;
    private JMenuItem item_course;
    private JMenu menu6;
    private JMenuItem item_aboutUs;
    private JMenu menu3;
    private JPanel panel1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
