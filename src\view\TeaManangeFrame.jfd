JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "55dlu, labelcompgap, 91dlu, labelcompgap, 88dlu, labelcompgap, 71dlu, labelcompgap, 68dlu, labelcompgap, 55dlu"
			"$rowSpecs": "48dlu, linegap, 154dlu, linegap, 42dlu, linegap, 39dlu, linegap, 41dlu"
		} ) {
			name: "this"
			"title": "教师信息管理"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "教师姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_query"
				"text": "查询"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/搜索.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
			} )
			add( new FormContainer( "javax.swing.JScrollPane", new FormLayoutManager( class javax.swing.JScrollPane ) ) {
				name: "scrollPane1"
				add( new FormComponent( "javax.swing.JTable" ) {
					name: "table1"
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"gridWidth": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
				"vAlign": sfield com.jgoodies.forms.layout.CellConstraints FILL
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "教师姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/老师.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name2"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "教师性别："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/性别.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormContainer( "javax.swing.JPanel", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
				"$columnSpecs": "default, labelcompgap, default"
				"$rowSpecs": "default"
			} ) {
				name: "panel1"
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_male"
					"text": "男"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 1
					"gridY": 1
				} )
				add( new FormComponent( "javax.swing.JRadioButton" ) {
					name: "rb_female"
					"text": "女"
				}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
					"gridX": 3
					"gridY": 1
				} )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "教师职称："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/职称评定.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_title"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label5"
				"text": "教师年龄："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/年龄.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_age"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label6"
				"text": "登录密码："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/密码.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "pf_psw"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 9
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_update"
				"text": "确认修改"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 9
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_delete"
				"text": "删除信息"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/删除.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 9
				"gridY": 9
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 810, 580 )
		} )
	}
}
