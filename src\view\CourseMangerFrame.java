/*
 * Created by <PERSON><PERSON>ormDesigner on Fri Jul 25 11:23:57 CST 2025
 */

package view;

import javax.swing.*;
import javax.swing.event.ListSelectionEvent;
import javax.swing.event.ListSelectionListener;
import javax.swing.table.DefaultTableModel;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.CourseDao;
import pojo.Course;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;


/**
 * <AUTHOR>
 */
public class CourseMangerFrame extends JFrame {
    private CourseDao courseDao;
    private List<Course> courseList;
    private static final int COL_ID = 0;
    private static final int COL_NAME = 1;
    private static final int COL_INFO = 4;

    public CourseMangerFrame() {
        courseDao = new CourseDao();
        initComponents();
        DefaultTableModel dtm = new DefaultTableModel();
        dtm.setDataVector(new Object[][]{},new String[]{"课程编号","课程名字","课程老师编号","最多学生数量","课程信息","选定数字"});
        table1.setModel(dtm);
        initEventHandlers();
        setTable(new Course()); // 初始加载所有课程
    }

    private void initEventHandlers() {
        // 查询按钮的事件处理（修复搜索功能）
        btn_query.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                String courseName = txt_name.getText().trim(); // 去除首尾空格
                Course course = new Course();
                course.setName(courseName);
                setTable(course); // 传入查询条件
            }
        });

        // 修改按钮的事件处理（增强空指针防护）
        btn_reset.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = table1.getSelectedRow();
                if (selectedRow < 0) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "请先选择要修改的课程！");
                    return;
                }
                // 安全获取课程ID（避免空指针）
                Object idObj = table1.getValueAt(selectedRow, COL_ID);
                if (idObj == null) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "选中的课程数据异常！");
                    return;
                }
                int courseId;
                try {
                    courseId = Integer.parseInt(idObj.toString());
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程ID格式错误！");
                    return;
                }
                String newName = txt_name1.getText().trim();
                if (newName.isEmpty()) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "请输入课程名称！");
                    return;
                }
                Course course = courseDao.getCourseById(courseId);
                if (course != null) {
                    course.setName(newName);
                    int result = courseDao.updateCourse(course);
                    if (result > 0) {
                        setTable(new Course()); // 刷新表格
                        JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程名称修改成功！");
                    } else {
                        JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程名称修改失败！");
                    }
                } else {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "未找到该课程！");
                }
            }
        });

        // 修改课程信息按钮的事件处理（同上，增强防护）
        btn_reset1.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                int selectedRow = table1.getSelectedRow();
                if (selectedRow < 0) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "请先选择要修改的课程！");
                    return;
                }
                Object idObj = table1.getValueAt(selectedRow, COL_ID);
                if (idObj == null) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "选中的课程数据异常！");
                    return;
                }
                int courseId;
                try {
                    courseId = Integer.parseInt(idObj.toString());
                } catch (NumberFormatException ex) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程ID格式错误！");
                    return;
                }
                String newInfo = txt_name2.getText().trim();
                if (newInfo.isEmpty()) {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "请输入课程信息！");
                    return;
                }
                Course course = courseDao.getCourseById(courseId);
                if (course != null) {
                    course.setInfo(newInfo);
                    int result = courseDao.updateCourse(course);
                    if (result > 0) {
                        setTable(new Course()); // 刷新表格
                        JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程信息修改成功！");
                    } else {
                        JOptionPane.showMessageDialog(CourseMangerFrame.this, "课程信息修改失败！");
                    }
                } else {
                    JOptionPane.showMessageDialog(CourseMangerFrame.this, "未找到该课程！");
                }
            }
        });

        // 表格选择事件处理（安全填充文本框）
        table1.getSelectionModel().addListSelectionListener(new ListSelectionListener() {
            @Override
            public void valueChanged(ListSelectionEvent e) {
                if (!e.getValueIsAdjusting()) {
                    int selectedRow = table1.getSelectedRow();
                    if (selectedRow >= 0) {
                        // 安全获取数据（避免空指针）
                        Object nameObj = table1.getValueAt(selectedRow, COL_NAME);
                        Object infoObj = table1.getValueAt(selectedRow, COL_INFO);
                        txt_name1.setText(nameObj != null ? nameObj.toString() : "");
                        txt_name2.setText(infoObj != null ? infoObj.toString() : "");
                    }
                }
            }
        });
    }

    // 修复表格数据加载逻辑（支持条件查询）
    private void setTable(Course queryCourse) {
        // 根据查询条件决定调用哪个DAO方法
        if (queryCourse.getName() == null || queryCourse.getName().trim().isEmpty()) {
            courseList = courseDao.getAllCourses(); // 查询所有
        } else {
            courseList = courseDao.getCoursesByName(queryCourse.getName()); // 按名称查询
        }
        DefaultTableModel dtm = (DefaultTableModel) table1.getModel();
        dtm.setRowCount(0); // 清空表格
        // 填充数据（使用Object[]更简洁）
        for (Course course : courseList) {
            Object[] row = {
                    course.getId(),
                    course.getName(),
                    course.getTeacher_id(),
                    course.getMax_student_num(),
                    course.getInfo(),
                    course.getSelected_num()
            };
            dtm.addRow(row);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        btn_query = new JButton();
        scrollPane1 = new JScrollPane();
        table1 = new JTable();
        label2 = new JLabel();
        txt_name1 = new JTextField();
        btn_reset = new JButton();
        label3 = new JLabel();
        txt_name2 = new JTextField();
        btn_reset1 = new JButton();

        //======== this ========
        {
            setTitle("\u8bfe\u7a0b\u4fe1\u606f\u7ba1\u7406");
            setIconImage(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")).getImage());
            var contentPane = getContentPane();
            contentPane.setLayout(new FormLayout(
                "55dlu, $lcgap, 91dlu, $lcgap, 88dlu, $lcgap, 71dlu, $lcgap, 68dlu, $lcgap, 55dlu",
                "48dlu, $lgap, 154dlu, $lgap, 42dlu, $lgap, 39dlu, $lgap, 41dlu"));

            //---- label1 ----
            label1.setText("\u8bfe\u7a0b\u540d\u79f0\uff1a");
            label1.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));
            contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
            contentPane.add(txt_name, CC.xywh(5, 1, 3, 1));

            //---- btn_query ----
            btn_query.setText("\u67e5\u8be2");
            btn_query.setIcon(new ImageIcon(getClass().getResource("/\u641c\u7d22.png")));
            contentPane.add(btn_query, CC.xy(9, 1));

            //======== scrollPane1 ========
            {
                scrollPane1.setViewportView(table1);
            }
            contentPane.add(scrollPane1, CC.xywh(3, 3, 7, 3, CC.FILL, CC.FILL));

            //---- label2 ----
            label2.setText("\u8bfe\u7a0b\u540d\u79f0\uff1a");
            label2.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));
            contentPane.add(label2, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
            contentPane.add(txt_name1, CC.xywh(5, 7, 3, 1));

            //---- btn_reset ----
            btn_reset.setText("\u4fee\u6539");
            btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
            contentPane.add(btn_reset, CC.xy(9, 7));

            //---- label3 ----
            label3.setText("\u8bfe\u7a0b\u4fe1\u606f\uff1a");
            label3.setIcon(new ImageIcon(getClass().getResource("/\u8bfe\u7a0b\u5217\u8868.png")));
            contentPane.add(label3, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
            contentPane.add(txt_name2, CC.xywh(5, 9, 3, 1));

            //---- btn_reset1 ----
            btn_reset1.setText("\u4fee\u6539");
            btn_reset1.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
            contentPane.add(btn_reset1, CC.xy(9, 9));
            pack();
            setLocationRelativeTo(getOwner());
        }
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JButton btn_query;
    private JScrollPane scrollPane1;
    private JTable table1;
    private JLabel label2;
    private JTextField txt_name1;
    private JButton btn_reset;
    private JLabel label3;
    private JTextField txt_name2;
    private JButton btn_reset1;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
