/*
 * Created by JFormDesigner on Mon Jul 07 09:55:15 CST 2025
 */

package view;

import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.TeacherDao;
import pojo.Teacher;
import util.StringUtil;

import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * <AUTHOR>
 */
public class AddTeaFrame extends JFrame {
    private ButtonGroup group;
    public AddTeaFrame() {
        //1、页面初始化的代码，放到构造方法的第一行
        initComponents();
        //2、单选按钮三选一的效果
        group=new ButtonGroup();
        group.add(rb_male);
        group.add(rb_female);
        //3、btn_sure进行ActionEvent事件处理
        btn_sure.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                //(1)从界面获取用户输入的数据
                String TeaName= txt_name.getText().toString();
                String TeaPsw= txt_psw.getText().toString();
                String TeaTitle= txt_title.getText().toString();
                int TeaAge= Integer.parseInt(txt_age.getText().toString());
                String sex;
                if (rb_male.isSelected()) {
                    sex = rb_male.getText();
                } else {
                    if (rb_female.isSelected()) sex = rb_female.getText();
                    else sex = rb_female.getText();
                }
                if(StringUtil.isEmpty(TeaName)){
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"教师姓名不能为空，请输教师姓名！！！");
                    return;
                }
                if(StringUtil.isEmpty(TeaPsw)){
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"登录密码不能为空，请输入登录密码！！！");
                    return;
                }
                if(StringUtil.isEmpty(TeaTitle)){
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"教师职称不能为空，请输教师职称！！！");
                    return;
                }
                if(TeaAge<=0){
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"年龄输入错误，请重新输入！！！");
                    return;
                }
                Teacher tea=new Teacher();
                tea.setName(TeaName);
                tea.setPassword(TeaPsw);
                tea.setSex(sex);
                tea.setAge(TeaAge);
                tea.setTitle(TeaTitle);
                //dao.addTeacher（tea）
                TeacherDao dao=new TeacherDao();
                boolean flag=dao.addTeacher(tea);
                //根据返回值完成对用户的提示
                if(flag){
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"恭喜您添加成功！！！");
                }else{
                    JOptionPane.showMessageDialog(AddTeaFrame.this,"添加失败");
                }
                //（4）清空界面
                resetValue();
            }
        });
        //btn_reset进行处理
        btn_reset.addActionListener(new ActionListener() {

            @Override
            public void actionPerformed(ActionEvent e) {
                resetValue();
            }
        });

    }

    private void resetValue() {
        txt_name.setText("");
        txt_psw.setText("");
        txt_title.setText("");
        group.clearSelection();
        rb_male.setSelected(true);
        rb_female.setSelected(true);
    }


    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        txt_name = new JTextField();
        label2 = new JLabel();
        panel1 = new JPanel();
        rb_male = new JRadioButton();
        rb_female = new JRadioButton();
        label3 = new JLabel();
        txt_title = new JTextField();
        label4 = new JLabel();
        txt_age = new JTextField();
        label5 = new JLabel();
        txt_psw = new JTextField();
        btn_sure = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u6559\u5e08\u6dfb\u52a0\u7a97\u53e3");
        setIconImage(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "45dlu, $lcgap, 83dlu, $lcgap, 92dlu, $lcgap, 52dlu",
            "42dlu, $lgap, 48dlu, $lgap, 49dlu, $lgap, 45dlu, $lgap, 43dlu, $lgap, 44dlu"));

        //---- label1 ----
        label1.setText("\u6559\u5e08\u59d3\u540d\uff1a");
        label1.setIcon(new ImageIcon(getClass().getResource("/\u8001\u5e08.png")));
        contentPane.add(label1, CC.xy(3, 1, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 1));

        //---- label2 ----
        label2.setText("\u6559\u5e08\u6027\u522b\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u6027\u522b.png")));
        contentPane.add(label2, CC.xy(3, 3, CC.RIGHT, CC.DEFAULT));

        //======== panel1 ========
        {
            panel1.setLayout(new FormLayout(
                "default, $lcgap, default",
                "default"));

            //---- rb_male ----
            rb_male.setText("\u7537");
            panel1.add(rb_male, CC.xy(1, 1));

            //---- rb_female ----
            rb_female.setText("\u5973");
            panel1.add(rb_female, CC.xy(3, 1));
        }
        contentPane.add(panel1, CC.xy(5, 3));

        //---- label3 ----
        label3.setText("\u6559\u5e08\u804c\u79f0\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u804c\u79f0\u8bc4\u5b9a.png")));
        contentPane.add(label3, CC.xy(3, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_title, CC.xy(5, 5));

        //---- label4 ----
        label4.setText("\u6559\u5e08\u5e74\u9f84\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/\u5e74\u9f84.png")));
        contentPane.add(label4, CC.xy(3, 7, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_age, CC.xy(5, 7));

        //---- label5 ----
        label5.setText("\u767b\u9646\u5bc6\u7801\uff1a");
        label5.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label5, CC.xy(3, 9, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_psw, CC.xy(5, 9));

        //---- btn_sure ----
        btn_sure.setText("\u786e\u8ba4\u6dfb\u52a0");
        btn_sure.setIcon(new ImageIcon(getClass().getResource("/\u786e\u8ba4.png")));
        contentPane.add(btn_sure, CC.xy(3, 11));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e\u8868\u5355");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(5, 11));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JTextField txt_name;
    private JLabel label2;
    private JPanel panel1;
    private JRadioButton rb_male;
    private JRadioButton rb_female;
    private JLabel label3;
    private JTextField txt_title;
    private JLabel label4;
    private JTextField txt_age;
    private JLabel label5;
    private JTextField txt_psw;
    private JButton btn_sure;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
