/*
 * Created by JFormDesigner on Sun Jun 29 16:12:38 CST 2025
 */

package view;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import javax.swing.*;
import com.jgoodies.forms.factories.*;
import com.jgoodies.forms.layout.*;
import dao.AdminDao;
import dao.StudentDao;
import dao.TeacherDao;
import pojo.Admin;
import pojo.Student;
import pojo.Teacher;
import util.StringUtil;

/**
 * <AUTHOR>
 */
public class LoginFrame extends JFrame implements ActionListener {
    public LoginFrame() {
        initComponents();
        //1、btn_login事件处理，事件源：btn_login   监听器：this
        btn_login.addActionListener(this);

        //2、btn_reset事件处理，事件源：btn_reset  监听器;this
        btn_reset.addActionListener(this);
    }

    @Override
    public void actionPerformed(ActionEvent e) {
        //获取用户点击的事件源
        JButton btn= (JButton) e.getSource();
        //分情况讨论
        if(btn==btn_login)//登录
        {
            //(1)从界面获取用户输入的信息
            String userName= txt_name.getText().toString();
            String password= pf_psw.getText().toString();
            String userType=cb_type.getSelectedItem().toString();
            if(StringUtil.isEmpty(userName))
            {
                JOptionPane.showMessageDialog(this,"用户名不能为空，请输入用户名！！！");
                return;
            }
            if(StringUtil.isEmpty(password))
            {
                JOptionPane.showMessageDialog(this,"密码不能为空，请输入密码！！！");
                return;
            }
            //（2）登陆验证dao.login（）
            if(userType.equals("系统管理员"))
            {
                AdminDao dao=new AdminDao();
                Admin admin=new Admin();
                admin.setName(userName);
                admin.setPassword(password);
               Admin rstAdmin= dao.login(admin);
               if(rstAdmin!=null)
               {
                   //登陆成功
                   JOptionPane.showMessageDialog(this,"欢迎【"+userType+"】"+userName+"登录本系统");
                   //关闭自身
                   this.dispose();
                   //打开主界面
                   new MainFrame(userType,rstAdmin).setVisible(true);
               }else{
                   //失败的提示
                   JOptionPane.showMessageDialog(this,"用户名或密码错误！！！！");
               }
            }
            // 补充教师登录逻辑
            else if(userType.equals("教师")){
                TeacherDao dao = new TeacherDao();
                Teacher teacher = new Teacher();
                teacher.setName(userName);
                teacher.setPassword(password);
                Teacher rstTeacher = dao.login(teacher);
                if(rstTeacher!=null){
                    JOptionPane.showMessageDialog(this,"欢迎【"+userType+"】"+userName+"登陆本系统");
                    this.dispose();
                    new TeaFrame(userType,rstTeacher).setVisible(true);
                }else{
                    JOptionPane.showMessageDialog(this,"用户名或密码错误！！！");
                }
            }
            // 补充学生登录逻辑
            else if(userType.equals("学生")){
                StudentDao dao = new StudentDao();
                Student student = new Student();
                student.setName(userName);
                student.setPassword(password);
                Student rstStudent = dao.login(student);
                if(rstStudent!=null){
                    JOptionPane.showMessageDialog(this,"欢迎【"+userType+"】"+userName+"登陆本系统");
                    this.dispose();
                    new StudentFrame(userType,rstStudent).setVisible(true);
                }else{
                    JOptionPane.showMessageDialog(this,"用户名或密码错误！！！");
                }
            }
        }
        if(btn==btn_reset)//重置
        {
            txt_name.setText("");
            pf_psw.setText("");
            cb_type.setSelectedIndex(0);
        }
    }

    private void initComponents() {
        // JFormDesigner - Component initialization - DO NOT MODIFY  //GEN-BEGIN:initComponents  @formatter:off
        label1 = new JLabel();
        label2 = new JLabel();
        txt_name = new JTextField();
        label3 = new JLabel();
        pf_psw = new JTextField();
        label4 = new JLabel();
        cb_type = new JComboBox<>();
        btn_login = new JButton();
        btn_reset = new JButton();

        //======== this ========
        setTitle("\u767b\u9646\u754c\u9762");
        setIconImage(new ImageIcon(getClass().getResource("/\u7528\u6237\u540d.png")).getImage());
        var contentPane = getContentPane();
        contentPane.setLayout(new FormLayout(
            "62dlu, $lcgap, 35dlu, $lcgap, 124dlu, $lcgap, 60dlu",
            "50dlu, 3*($lgap, 33dlu), $lgap, 38dlu"));

        //---- label1 ----
        label1.setText("\u5b66\u751f\u4fe1\u606f\u7cfb\u7edf\u767b\u9646\u754c\u9762");
        label1.setIcon(new ImageIcon(getClass().getResource("/logo.png")));
        label1.setFont(label1.getFont().deriveFont(label1.getFont().getStyle() | Font.BOLD, label1.getFont().getSize() + 14f));
        contentPane.add(label1, CC.xywh(1, 1, 7, 1, CC.CENTER, CC.DEFAULT));

        //---- label2 ----
        label2.setText("\u7528\u6237\u540d\uff1a");
        label2.setIcon(new ImageIcon(getClass().getResource("/\u7528\u6237\u540d.png")));
        contentPane.add(label2, CC.xy(1, 3, CC.RIGHT, CC.DEFAULT));
        contentPane.add(txt_name, CC.xy(5, 3));

        //---- label3 ----
        label3.setText("\u5bc6\u7801\uff1a");
        label3.setIcon(new ImageIcon(getClass().getResource("/\u5bc6\u7801.png")));
        contentPane.add(label3, CC.xy(1, 5, CC.RIGHT, CC.DEFAULT));
        contentPane.add(pf_psw, CC.xy(5, 5));

        //---- label4 ----
        label4.setText("\u7528\u6237\u7c7b\u578b\uff1a");
        label4.setIcon(new ImageIcon(getClass().getResource("/userType.png")));
        contentPane.add(label4, CC.xy(1, 7, CC.RIGHT, CC.DEFAULT));

        //---- cb_type ----
        cb_type.setModel(new DefaultComboBoxModel<>(new String[] {
            "\u7cfb\u7edf\u7ba1\u7406\u5458",
            "\u6559\u5e08",
            "\u5b66\u751f"
        }));
        contentPane.add(cb_type, CC.xy(5, 7));

        //---- btn_login ----
        btn_login.setText("\u767b\u5f55");
        btn_login.setIcon(new ImageIcon(getClass().getResource("/\u767b\u5f55.png")));
        contentPane.add(btn_login, CC.xy(1, 9, CC.RIGHT, CC.DEFAULT));

        //---- btn_reset ----
        btn_reset.setText("\u91cd\u7f6e");
        btn_reset.setIcon(new ImageIcon(getClass().getResource("/\u91cd\u7f6e.png")));
        contentPane.add(btn_reset, CC.xy(5, 9, CC.RIGHT, CC.DEFAULT));
        pack();
        setLocationRelativeTo(getOwner());
        // JFormDesigner - End of component initialization  //GEN-END:initComponents  @formatter:on
    }

    // JFormDesigner - Variables declaration - DO NOT MODIFY  //GEN-BEGIN:variables  @formatter:off
    private JLabel label1;
    private JLabel label2;
    private JTextField txt_name;
    private JLabel label3;
    private JTextField pf_psw;
    private JLabel label4;
    private JComboBox<String> cb_type;
    private JButton btn_login;
    private JButton btn_reset;
    // JFormDesigner - End of variables declaration  //GEN-END:variables  @formatter:on
}
