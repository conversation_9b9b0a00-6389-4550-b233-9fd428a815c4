JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "93dlu, labelcompgap, 104dlu, labelcompgap, 81dlu"
			"$rowSpecs": "49dlu, linegap, 49dlu, linegap, 58dlu"
		} ) {
			name: "this"
			"title": "添加班级信息"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/班级介绍.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "班级名称："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "班级信息："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级介绍.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridY": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_info"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_sure"
				"text": "提交"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "重置"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 520, 300 )
		} )
	}
}
