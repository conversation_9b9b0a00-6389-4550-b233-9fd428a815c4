/*
 Navicat Premium Data Transfer

 Source Server         : yang_666
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : 127.0.0.1:3306
 Source Schema         : db_stu

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 26/07/2025 16:03:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for s_admin
-- ----------------------------
DROP TABLE IF EXISTS `s_admin`;
CREATE TABLE `s_admin`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `password` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `createDate` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_admin
-- ----------------------------
INSERT INTO `s_admin` VALUES (1, 'admin', '123', '2018-03-17 14:24:09');

-- ----------------------------
-- Table structure for s_attendance
-- ----------------------------
DROP TABLE IF EXISTS `s_attendance`;
CREATE TABLE `s_attendance`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL,
  `course_id` int NOT NULL,
  `attendance_date` varchar(12) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `student_attendance_foreign`(`student_id` ASC) USING BTREE,
  INDEX `course_attendance_foreign`(`course_id` ASC) USING BTREE,
  CONSTRAINT `course_attendance_foreign` FOREIGN KEY (`course_id`) REFERENCES `s_course` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `student_attendance_foreign` FOREIGN KEY (`student_id`) REFERENCES `s_student` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_attendance
-- ----------------------------
INSERT INTO `s_attendance` VALUES (1, 1, 1, '2018-05-17');
INSERT INTO `s_attendance` VALUES (2, 1, 1, '2018-04-17');
INSERT INTO `s_attendance` VALUES (3, 1, 4, '2018-04-17');
INSERT INTO `s_attendance` VALUES (4, 1, 3, '2018-04-17');
INSERT INTO `s_attendance` VALUES (5, 1, 1, '2018-04-18');
INSERT INTO `s_attendance` VALUES (6, 1, 4, '2018-04-18');
INSERT INTO `s_attendance` VALUES (7, 1, 3, '2018-04-18');
INSERT INTO `s_attendance` VALUES (8, 4, 1, '2018-04-19');
INSERT INTO `s_attendance` VALUES (9, 4, 2, '2018-04-19');
INSERT INTO `s_attendance` VALUES (10, 4, 6, '2018-04-19');
INSERT INTO `s_attendance` VALUES (11, 4, 4, '2018-04-19');
INSERT INTO `s_attendance` VALUES (12, 3, 1, '2018-04-19');
INSERT INTO `s_attendance` VALUES (15, 3, 4, '2018-04-19');
INSERT INTO `s_attendance` VALUES (16, 1, 1, '2018-04-19');
INSERT INTO `s_attendance` VALUES (17, 1, 4, '2018-04-19');
INSERT INTO `s_attendance` VALUES (18, 1, 3, '2018-04-19');
INSERT INTO `s_attendance` VALUES (19, 1, 2, '2018-04-19');
INSERT INTO `s_attendance` VALUES (20, 3, 6, '2018-04-19');
INSERT INTO `s_attendance` VALUES (21, 3, 5, '2018-04-19');
INSERT INTO `s_attendance` VALUES (22, 1, 1, '2018-04-20');
INSERT INTO `s_attendance` VALUES (23, 1, 4, '2018-04-20');
INSERT INTO `s_attendance` VALUES (24, 1, 3, '2018-04-20');
INSERT INTO `s_attendance` VALUES (25, 1, 2, '2018-04-21');
INSERT INTO `s_attendance` VALUES (26, 1, 1, '2018-04-21');
INSERT INTO `s_attendance` VALUES (27, 1, 1, '2018-05-03');

-- ----------------------------
-- Table structure for s_class
-- ----------------------------
DROP TABLE IF EXISTS `s_class`;
CREATE TABLE `s_class`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `info` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_class
-- ----------------------------
INSERT INTO `s_class` VALUES (2, '22软件一班', '这是软件一班。');
INSERT INTO `s_class` VALUES (6, '22软件二班', '放松放松');
INSERT INTO `s_class` VALUES (7, '22计科一班', '');
INSERT INTO `s_class` VALUES (9, '22计科二班', '');
INSERT INTO `s_class` VALUES (10, '22计科三班', NULL);
INSERT INTO `s_class` VALUES (11, '22计科四班', NULL);

-- ----------------------------
-- Table structure for s_course
-- ----------------------------
DROP TABLE IF EXISTS `s_course`;
CREATE TABLE `s_course`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `teacher_id` int NOT NULL,
  `max_student_num` int NOT NULL,
  `info` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL,
  `selected_num` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `teacher_foreign`(`teacher_id` ASC) USING BTREE,
  CONSTRAINT `teacher_foreign` FOREIGN KEY (`teacher_id`) REFERENCES `s_teacher` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_course
-- ----------------------------
INSERT INTO `s_course` VALUES (1, '大学数学', 3, 50, '数学课程', 3);
INSERT INTO `s_course` VALUES (2, '大学英语', 4, 45, '大英3', 3);
INSERT INTO `s_course` VALUES (3, '原子弹原理技术', 5, 66, '制造原子弹技术，很牛逼！', 1);
INSERT INTO `s_course` VALUES (4, '软件工程', 3, 66, '666', 3);
INSERT INTO `s_course` VALUES (5, '计算机原理', 4, 45, '计算机组成原理，非常重要。', 2);
INSERT INTO `s_course` VALUES (6, '人工智能', 5, 2, '人工智能课程。', 2);
INSERT INTO `s_course` VALUES (7, 'web开发', 3, 60, 'web开发', 0);

-- ----------------------------
-- Table structure for s_score
-- ----------------------------
DROP TABLE IF EXISTS `s_score`;
CREATE TABLE `s_score`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NOT NULL,
  `course_id` int NOT NULL,
  `score` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `student_score_foreign`(`student_id` ASC) USING BTREE,
  INDEX `course_id_score_foreign`(`course_id` ASC) USING BTREE,
  CONSTRAINT `course_id_score_foreign` FOREIGN KEY (`course_id`) REFERENCES `s_course` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `student_score_foreign` FOREIGN KEY (`student_id`) REFERENCES `s_student` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_score
-- ----------------------------
INSERT INTO `s_score` VALUES (1, 1, 1, 78);
INSERT INTO `s_score` VALUES (2, 1, 4, 87);
INSERT INTO `s_score` VALUES (4, 1, 2, 78);
INSERT INTO `s_score` VALUES (5, 1, 3, 98);
INSERT INTO `s_score` VALUES (6, 1, 5, 78);
INSERT INTO `s_score` VALUES (7, 3, 6, 89);
INSERT INTO `s_score` VALUES (8, 3, 1, 60);
INSERT INTO `s_score` VALUES (9, 4, 1, 87);
INSERT INTO `s_score` VALUES (10, 3, 2, 78);
INSERT INTO `s_score` VALUES (11, 4, 2, 56);
INSERT INTO `s_score` VALUES (12, 3, 4, 56);
INSERT INTO `s_score` VALUES (13, 4, 4, 77);
INSERT INTO `s_score` VALUES (14, 3, 5, 67);

-- ----------------------------
-- Table structure for s_selected_course
-- ----------------------------
DROP TABLE IF EXISTS `s_selected_course`;
CREATE TABLE `s_selected_course`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `student_id` int NULL DEFAULT NULL,
  `course_id` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `student_foreign`(`student_id` ASC) USING BTREE,
  INDEX `course_foreign`(`course_id` ASC) USING BTREE,
  CONSTRAINT `course_foreign` FOREIGN KEY (`course_id`) REFERENCES `s_course` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `student_foreign` FOREIGN KEY (`student_id`) REFERENCES `s_student` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_selected_course
-- ----------------------------
INSERT INTO `s_selected_course` VALUES (12, 1, 1);
INSERT INTO `s_selected_course` VALUES (13, 1, 4);
INSERT INTO `s_selected_course` VALUES (14, 1, 3);
INSERT INTO `s_selected_course` VALUES (15, 3, 1);
INSERT INTO `s_selected_course` VALUES (16, 3, 6);
INSERT INTO `s_selected_course` VALUES (17, 3, 5);
INSERT INTO `s_selected_course` VALUES (18, 3, 2);
INSERT INTO `s_selected_course` VALUES (19, 4, 1);
INSERT INTO `s_selected_course` VALUES (20, 4, 2);
INSERT INTO `s_selected_course` VALUES (21, 1, 2);
INSERT INTO `s_selected_course` VALUES (22, 3, 4);
INSERT INTO `s_selected_course` VALUES (23, 4, 6);
INSERT INTO `s_selected_course` VALUES (24, 4, 4);
INSERT INTO `s_selected_course` VALUES (25, 1, 5);

-- ----------------------------
-- Table structure for s_student
-- ----------------------------
DROP TABLE IF EXISTS `s_student`;
CREATE TABLE `s_student`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `classId` int NOT NULL,
  `password` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `sex` varchar(8) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `class_foreign`(`classId` ASC) USING BTREE,
  CONSTRAINT `class_foreign` FOREIGN KEY (`classId`) REFERENCES `s_class` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_student
-- ----------------------------
INSERT INTO `s_student` VALUES (1, '张三', 6, '123', '保密');
INSERT INTO `s_student` VALUES (3, '李四', 6, '11', '女');
INSERT INTO `s_student` VALUES (4, '王麻子', 7, '1', '保密');
INSERT INTO `s_student` VALUES (6, '罗静', 6, '12', '女');
INSERT INTO `s_student` VALUES (8, '李四', 2, '123', '男');
INSERT INTO `s_student` VALUES (9, '11', 9, '123', '女');
INSERT INTO `s_student` VALUES (10, 'uu', 2, '1234', '女');
INSERT INTO `s_student` VALUES (11, 'qq', 2, '1234', '男');
INSERT INTO `s_student` VALUES (12, 'ww', 2, '123', '保密');
INSERT INTO `s_student` VALUES (13, '去去去', 2, '1234', '男');

-- ----------------------------
-- Table structure for s_teacher
-- ----------------------------
DROP TABLE IF EXISTS `s_teacher`;
CREATE TABLE `s_teacher`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `sex` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `title` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  `age` int NOT NULL,
  `password` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of s_teacher
-- ----------------------------
INSERT INTO `s_teacher` VALUES (3, '张三', '男', '教授', 35, '1');
INSERT INTO `s_teacher` VALUES (4, '李四', '男', '博导', 58, '222');
INSERT INTO `s_teacher` VALUES (5, '王麻子', '男', '讲师', 28, '000000');

SET FOREIGN_KEY_CHECKS = 1;
