JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "93dlu, labelcompgap, 104dlu, labelcompgap, 81dlu"
			"$rowSpecs": "49dlu, linegap, 49dlu, linegap, 49dlu, linegap, 58dlu"
		} ) {
			name: "this"
			"title": "成绩输入"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "选择学生："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_student"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 1
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "选择课程："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/课程列表.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_course"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "输入成绩："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级介绍.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_score"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_sure"
				"text": "确认添加"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "重置表单"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 400, 300 )
		} )
	}
}