JFDML JFormDesigner: "8.2.4.0.393" Java: "21.0.6" encoding: "UTF-8"

new FormModel {
	contentType: "form/swing"
	root: new FormRoot {
		add( new FormWindow( "javax.swing.JFrame", new FormLayoutManager( class com.jgoodies.forms.layout.FormLayout ) {
			"$columnSpecs": "82dlu, labelcompgap, 33dlu, labelcompgap, 33dlu, labelcompgap, 50dlu, labelcompgap, 37dlu"
			"$rowSpecs": "53dlu, linegap, 35dlu, linegap, 35dlu, linegap, 35dlu, linegap, 35dlu"
		} ) {
			name: "this"
			"title": "添加学生窗口"
			"iconImage": new com.jformdesigner.model.SwingIcon( 0, "/添加.png" )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label1"
				"text": "学生姓名："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/学生管理.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 1
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_name"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridWidth": 6
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label2"
				"text": "所属班级："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/班级名称.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 3
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JComboBox" ) {
				name: "cb_class"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 3
				"gridWidth": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label3"
				"text": "登录密码："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/密码.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 5
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JTextField" ) {
				name: "txt_psw"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 5
				"gridWidth": 5
			} )
			add( new FormComponent( "javax.swing.JLabel" ) {
				name: "label4"
				"text": "学生性别："
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/性别.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 7
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JCheckBox" ) {
				name: "rb_male"
				"text": "男"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 3
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JCheckBox" ) {
				name: "rb_female"
				"text": "女"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 5
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JCheckBox" ) {
				name: "rb_secret"
				"text": "保密"
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 7
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_sure"
				"text": "确认"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/确认.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 1
				"gridY": 9
				"hAlign": sfield com.jgoodies.forms.layout.CellConstraints RIGHT
			} )
			add( new FormComponent( "javax.swing.JButton" ) {
				name: "btn_reset"
				"text": "重置"
				"icon": new com.jformdesigner.model.SwingIcon( 0, "/重置.png" )
			}, new FormLayoutConstraints( class com.jgoodies.forms.layout.CellConstraints ) {
				"gridX": 7
				"gridY": 9
			} )
		}, new FormLayoutConstraints( null ) {
			"location": new java.awt.Point( 0, 0 )
			"size": new java.awt.Dimension( 445, 365 )
		} )
	}
}
